#!/usr/bin/env python3
"""
Analyze translation calculation issues in HybridCornerAlignmentSystem.
"""

import numpy as np
import cv2
from hybrid_corner_alignment import GeometricHasher, HybridFeature, HybridCornerAlignmentSystem

def create_simple_test_case():
    """Create a simple test case to analyze translation calculation"""
    
    # Create 4 reference features in a square pattern
    ref_features = [
        HybridFeature("ref_0", "edge_keypoint", 100.0, 100.0, 100, 100, 0.8,
                     keypoint_data={'response': 50, 'angle': 0, 'octave': 0, 'size': 8}),
        HybridFeature("ref_1", "edge_keypoint", 200.0, 100.0, 200, 100, 0.8,
                     keypoint_data={'response': 50, 'angle': 0, 'octave': 0, 'size': 8}),
        HybridFeature("ref_2", "edge_keypoint", 200.0, 200.0, 200, 200, 0.8,
                     keypoint_data={'response': 50, 'angle': 0, 'octave': 0, 'size': 8}),
        HybridFeature("ref_3", "edge_keypoint", 100.0, 200.0, 100, 200, 0.8,
                     keypoint_data={'response': 50, 'angle': 0, 'octave': 0, 'size': 8}),
    ]
    
    # Apply known transformation: translation (20, 10) and rotation 5°
    translation = np.array([20.0, 10.0])
    rotation_degrees = 5.0
    rotation_radians = np.radians(rotation_degrees)
    
    cos_theta = np.cos(rotation_radians)
    sin_theta = np.sin(rotation_radians)
    rotation_matrix = np.array([[cos_theta, -sin_theta], [sin_theta, cos_theta]])
    
    center = np.array([150.0, 150.0])  # Center of square
    
    query_features = []
    for i, ref_feat in enumerate(ref_features):
        ref_pos = np.array([ref_feat.x_um, ref_feat.y_um])
        
        # Apply transformation: rotate around center, then translate
        transformed_pos = rotation_matrix @ (ref_pos - center) + center + translation
        
        query_feature = HybridFeature(
            f"query_{i}", "edge_keypoint",
            transformed_pos[0], transformed_pos[1],
            int(transformed_pos[0]), int(transformed_pos[1]),
            0.8,
            keypoint_data={'response': 50, 'angle': 0, 'octave': 0, 'size': 8}
        )
        query_features.append(query_feature)
    
    return ref_features, query_features, translation, rotation_degrees, center

def analyze_current_method():
    """Analyze the current centroid-based transformation calculation"""
    print("Analyzing Current Centroid-Based Method")
    print("=" * 50)
    
    ref_features, query_features, expected_translation, expected_rotation, center = create_simple_test_case()
    
    print(f"Test setup:")
    print(f"  Expected translation: {expected_translation}")
    print(f"  Expected rotation: {expected_rotation}°")
    print(f"  Rotation center: {center}")
    
    # Create geometric hasher and find matches
    hasher = GeometricHasher(hash_precision=0, debug=False, match_threshold=0.3)
    hash_table = hasher.generate_hash_table(ref_features)
    matches = hasher.find_matches(query_features, min_matches=3)
    
    print(f"\nMatching results:")
    print(f"  Found {len(matches)} matches")
    
    if len(matches) >= 3:
        # Analyze the current method
        print(f"\nCurrent method analysis (using triangle centroids):")
        
        src_points = []
        dst_points = []
        
        for i, match in enumerate(matches[:3]):
            # Reference points (destination) - centroids
            ref_triplet = match['reference_triplet']['features']
            ref_centroid = np.mean([(f.x_um, f.y_um) for f in ref_triplet], axis=0)
            dst_points.append(ref_centroid)
            
            # Current points (source) - centroids
            query_triplet = match['query_triplet']
            query_centroid = np.mean([(f.x_um, f.y_um) for f in query_triplet], axis=0)
            src_points.append(query_centroid)
            
            print(f"  Match {i+1}:")
            print(f"    Reference centroid: ({ref_centroid[0]:.1f}, {ref_centroid[1]:.1f})")
            print(f"    Query centroid: ({query_centroid[0]:.1f}, {query_centroid[1]:.1f})")
        
        src_points = np.array(src_points, dtype=np.float32)
        dst_points = np.array(dst_points, dtype=np.float32)
        
        # Calculate transformation using current method
        matrix = cv2.getAffineTransform(src_points, dst_points)
        
        # Extract parameters
        scale_x = np.sqrt(matrix[0, 0]**2 + matrix[1, 0]**2)
        scale_y = np.sqrt(matrix[0, 1]**2 + matrix[1, 1]**2)
        rotation_calc = np.degrees(np.arctan2(matrix[1, 0], matrix[0, 0]))
        translation_calc = matrix[:, 2]
        
        print(f"\nCurrent method results:")
        print(f"  Calculated translation: ({translation_calc[0]:.1f}, {translation_calc[1]:.1f})")
        print(f"  Calculated rotation: {rotation_calc:.1f}°")
        print(f"  Scale factors: ({scale_x:.3f}, {scale_y:.3f})")
        
        # Calculate errors
        translation_error = np.linalg.norm(translation_calc - (-expected_translation))
        rotation_error = abs(rotation_calc - (-expected_rotation))
        
        print(f"\nErrors (note: calculated is inverse transformation):")
        print(f"  Translation error: {translation_error:.1f} pixels")
        print(f"  Rotation error: {rotation_error:.1f}°")
        
        return matrix, src_points, dst_points
    
    return None, None, None

def analyze_direct_feature_method():
    """Analyze using direct feature correspondences instead of centroids"""
    print(f"\n" + "=" * 50)
    print("Analyzing Direct Feature Correspondence Method")
    print("=" * 50)
    
    ref_features, query_features, expected_translation, expected_rotation, center = create_simple_test_case()
    
    # Create geometric hasher and find matches
    hasher = GeometricHasher(hash_precision=0, debug=False, match_threshold=0.3)
    hash_table = hasher.generate_hash_table(ref_features)
    matches = hasher.find_matches(query_features, min_matches=3)
    
    if len(matches) >= 3:
        print(f"Direct feature correspondence method:")
        
        # Extract individual feature correspondences from the matches
        src_points = []
        dst_points = []
        
        # Get all unique feature correspondences from the matches
        feature_pairs = set()
        
        for match in matches:
            ref_triplet = match['reference_triplet']['features']
            query_triplet = match['query_triplet']
            
            # Try to establish correspondences between individual features
            # For this simple test, we can use position-based matching
            for ref_feat in ref_triplet:
                for query_feat in query_triplet:
                    # Calculate distance between features
                    ref_pos = np.array([ref_feat.x_um, ref_feat.y_um])
                    query_pos = np.array([query_feat.x_um, query_feat.y_um])
                    
                    # For our test case, we can match by applying inverse transformation
                    # and finding closest features
                    pair_key = (ref_feat.id, query_feat.id)
                    if pair_key not in feature_pairs:
                        feature_pairs.add(pair_key)
                        src_points.append([query_feat.x_um, query_feat.y_um])
                        dst_points.append([ref_feat.x_um, ref_feat.y_um])
        
        # Use only the first 3 correspondences for affine transform
        if len(src_points) >= 3:
            src_points = np.array(src_points[:3], dtype=np.float32)
            dst_points = np.array(dst_points[:3], dtype=np.float32)
            
            print(f"Feature correspondences:")
            for i in range(3):
                print(f"  Pair {i+1}: ({src_points[i][0]:.1f}, {src_points[i][1]:.1f}) → ({dst_points[i][0]:.1f}, {dst_points[i][1]:.1f})")
            
            # Calculate transformation using direct features
            matrix = cv2.getAffineTransform(src_points, dst_points)
            
            # Extract parameters
            scale_x = np.sqrt(matrix[0, 0]**2 + matrix[1, 0]**2)
            scale_y = np.sqrt(matrix[0, 1]**2 + matrix[1, 1]**2)
            rotation_calc = np.degrees(np.arctan2(matrix[1, 0], matrix[0, 0]))
            translation_calc = matrix[:, 2]
            
            print(f"\nDirect feature method results:")
            print(f"  Calculated translation: ({translation_calc[0]:.1f}, {translation_calc[1]:.1f})")
            print(f"  Calculated rotation: {rotation_calc:.1f}°")
            print(f"  Scale factors: ({scale_x:.3f}, {scale_y:.3f})")
            
            # Calculate errors
            translation_error = np.linalg.norm(translation_calc - (-expected_translation))
            rotation_error = abs(rotation_calc - (-expected_rotation))
            
            print(f"\nErrors (note: calculated is inverse transformation):")
            print(f"  Translation error: {translation_error:.1f} pixels")
            print(f"  Rotation error: {rotation_error:.1f}°")

def analyze_coordinate_systems():
    """Analyze different coordinate system interpretations"""
    print(f"\n" + "=" * 50)
    print("Analyzing Coordinate System Issues")
    print("=" * 50)
    
    ref_features, query_features, expected_translation, expected_rotation, center = create_simple_test_case()
    
    print(f"Feature positions:")
    print(f"Reference features:")
    for i, feat in enumerate(ref_features):
        print(f"  {feat.id}: ({feat.x_um:.1f}, {feat.y_um:.1f})")
    
    print(f"Query features (after transformation):")
    for i, feat in enumerate(query_features):
        print(f"  {feat.id}: ({feat.x_um:.1f}, {feat.y_um:.1f})")
    
    # Calculate actual transformation between corresponding features
    print(f"\nActual feature displacements:")
    for i in range(len(ref_features)):
        ref_pos = np.array([ref_features[i].x_um, ref_features[i].y_um])
        query_pos = np.array([query_features[i].x_um, query_features[i].y_um])
        displacement = query_pos - ref_pos
        print(f"  Feature {i}: displacement = ({displacement[0]:.1f}, {displacement[1]:.1f})")
    
    # Calculate centroid displacement
    ref_centroid = np.mean([(f.x_um, f.y_um) for f in ref_features], axis=0)
    query_centroid = np.mean([(f.x_um, f.y_um) for f in query_features], axis=0)
    centroid_displacement = query_centroid - ref_centroid
    
    print(f"\nCentroid displacement:")
    print(f"  Reference centroid: ({ref_centroid[0]:.1f}, {ref_centroid[1]:.1f})")
    print(f"  Query centroid: ({query_centroid[0]:.1f}, {query_centroid[1]:.1f})")
    print(f"  Displacement: ({centroid_displacement[0]:.1f}, {centroid_displacement[1]:.1f})")
    print(f"  Expected translation: {expected_translation}")

def main():
    print("Translation Calculation Analysis")
    print("=" * 50)
    
    analyze_current_method()
    analyze_direct_feature_method()
    analyze_coordinate_systems()

if __name__ == "__main__":
    main()
