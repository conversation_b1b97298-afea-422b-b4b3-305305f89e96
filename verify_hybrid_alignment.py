#!/usr/bin/env python3
"""
Hybrid Corner Alignment Verification Tool

This script provides a comprehensive verification tool for the hybrid corner alignment system,
following the GUI design patterns from verify_edge_detection.py. It tests both CNN-based flake
detection and ORB keypoint detection, calculates transformation matrices, and provides detailed
visualization of the alignment process.

Key Features:
- Dual image input system (live camera capture + file selection)
- Hybrid feature detection (CNN flakes + ORB edge keypoints)
- Transformation matrix calculation with geometric hashing
- Side-by-side image comparison with feature annotations
- Detailed calculation display and export functionality

Author: Augment Agent
Date: 2025-07-02
"""

import numpy as np
import cv2
import os
import sys
import argparse
import time
import json
from pathlib import Path
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                            QTextEdit, QProgressBar, QFileDialog, QMessageBox,
                            QGroupBox, QFrame, QScrollArea, QSplitter)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QPixmap, QImage, QFont, QPalette, QColor
import threading

# Import required modules
try:
    from hybrid_corner_alignment import HybridCornerAlignmentSystem, HybridFeature, EdgeKeypointDetector
    from edge_detection import EdgeDetector, BackgroundEdgeDetector, CannyEdgeDetector
    from config import DEFAULT_REGION, DETECTION_API_URL, DETECTION_API_KEY, DETECTION_MODEL_ID, SELECTED_CLASSES
    from inference_sdk import InferenceHTTPClient
    from camera_manager import CentralizedCameraManager
    import supervision as sv
    print("Successfully imported hybrid corner alignment modules!")
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure you're running this from the directory with the hybrid alignment files.")
    sys.exit(1)


class ImageProcessingWorker(QThread):
    """Worker thread for image processing operations"""
    
    # Signals for communication with main thread
    progress_update = pyqtSignal(str)  # Status message
    feature_detection_complete = pyqtSignal(dict)  # Feature detection results
    alignment_complete = pyqtSignal(dict)  # Alignment calculation results
    error_occurred = pyqtSignal(str)  # Error message
    
    def __init__(self, image1, image2, image1_source, image2_source):
        super().__init__()
        self.image1 = image1
        self.image2 = image2
        self.image1_source = image1_source
        self.image2_source = image2_source
        
        # Initialize detection systems
        self.detection_client = InferenceHTTPClient(
            api_url=DETECTION_API_URL,
            api_key=DETECTION_API_KEY
        )
        self.edge_detector = CannyEdgeDetector(debug=True)
        self.keypoint_detector = EdgeKeypointDetector(max_keypoints=500, debug=True)
        
        # Mock stage controller for alignment system
        self.mock_stage = MockStageController()
        self.alignment_system = HybridCornerAlignmentSystem(
            stage_controller=self.mock_stage,
            region=DEFAULT_REGION,
            status_callback=self.progress_update.emit,
            debug=True
        )
    
    def run(self):
        """Main processing pipeline"""
        try:
            # Step 1: Feature detection on both images
            self.progress_update.emit("Detecting features in first image...")
            features1 = self.detect_hybrid_features(self.image1, "Image 1")
            
            self.progress_update.emit("Detecting features in second image...")
            features2 = self.detect_hybrid_features(self.image2, "Image 2")
            
            # Emit feature detection results
            feature_results = {
                'image1_features': features1,
                'image2_features': features2,
                'image1_source': self.image1_source,
                'image2_source': self.image2_source
            }
            self.feature_detection_complete.emit(feature_results)
            
            # Step 2: Calculate transformation matrix
            self.progress_update.emit("Calculating transformation matrix...")
            alignment_results = self.calculate_transformation(features1, features2)
            
            # Step 3: Create visualization
            self.progress_update.emit("Creating visualization...")
            visualization = self.create_comparison_visualization(
                self.image1, self.image2, features1, features2, alignment_results
            )
            alignment_results['visualization'] = visualization
            
            self.alignment_complete.emit(alignment_results)
            self.progress_update.emit("✓ Hybrid corner alignment verification completed!")
            
        except Exception as e:
            self.error_occurred.emit(f"Processing error: {str(e)}")
    
    def detect_hybrid_features(self, image, image_name):
        """Detect both CNN flakes and ORB edge keypoints"""
        features = {
            'flakes': [],
            'edge_keypoints': [],
            'total_features': 0,
            'processing_time': 0,
            'annotated_image': None
        }
        
        start_time = time.time()
        
        try:
            # CNN-based flake detection
            self.progress_update.emit(f"Running CNN flake detection on {image_name}...")
            result = self.detection_client.infer(image, model_id=DETECTION_MODEL_ID)
            
            # Process detection results
            svd = sv.Detections.from_inference(result)
            svd = svd[np.isin(svd.class_id, list(SELECTED_CLASSES))]
            
            # Create annotated image
            polygon_annotator = sv.PolygonAnnotator()
            label_annotator = sv.LabelAnnotator()
            annotated = polygon_annotator.annotate(scene=image.copy(), detections=svd)
            annotated = label_annotator.annotate(scene=annotated, detections=svd)
            
            # Convert detections to flake objects
            h, w = image.shape[:2]
            for i, (bbox, class_id, confidence) in enumerate(zip(svd.xyxy, svd.class_id, svd.confidence)):
                x1, y1, x2, y2 = bbox
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2
                
                flake = {
                    'id': f"flake_{i}",
                    'center_x': float(center_x),
                    'center_y': float(center_y),
                    'confidence': float(confidence),
                    'class_id': int(class_id),
                    'bbox': [float(x1), float(y1), float(x2), float(y2)]
                }
                features['flakes'].append(flake)
            
            # ORB edge keypoint detection
            self.progress_update.emit(f"Running ORB edge keypoint detection on {image_name}...")
            
            # Create edge mask using Canny detector
            edge_result = self.edge_detector.detect_edges_with_line_fitting(image)
            if edge_result and 'edge_image' in edge_result:
                edge_mask = edge_result['edge_image']
            else:
                # Fallback: create simple edge mask
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
                edge_mask = cv2.Canny(gray, 50, 150)
            
            # Detect ORB keypoints on edges
            keypoints, descriptors = self.keypoint_detector.orb.detectAndCompute(image, edge_mask)
            
            # Convert keypoints to feature format
            for i, kp in enumerate(keypoints):
                keypoint_feature = {
                    'id': f"keypoint_{i}",
                    'x': float(kp.pt[0]),
                    'y': float(kp.pt[1]),
                    'response': float(kp.response),
                    'angle': float(kp.angle),
                    'octave': int(kp.octave),
                    'size': float(kp.size)
                }
                features['edge_keypoints'].append(keypoint_feature)
            
            # Draw keypoints on annotated image
            annotated = cv2.drawKeypoints(annotated, keypoints, None, 
                                        color=(0, 255, 255), flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS)
            
            features['annotated_image'] = annotated
            features['total_features'] = len(features['flakes']) + len(features['edge_keypoints'])
            features['processing_time'] = time.time() - start_time
            
            self.progress_update.emit(f"{image_name}: Found {len(features['flakes'])} flakes, {len(features['edge_keypoints'])} keypoints")
            
        except Exception as e:
            self.progress_update.emit(f"Error detecting features in {image_name}: {str(e)}")
            features['error'] = str(e)
        
        return features
    
    def calculate_transformation(self, features1, features2):
        """Calculate transformation matrix between two feature sets using real HybridCornerAlignmentSystem"""
        results = {
            'success': False,
            'transformation': None,
            'confidence': 0.0,
            'matched_features': 0,
            'processing_time': 0,
            'error': None,
            'matrix': None,
            'matches': []
        }

        start_time = time.time()

        try:
            # Convert detected features to HybridFeature objects
            self.progress_update.emit("Converting features to HybridFeature format...")
            reference_features = self.convert_to_hybrid_features(features1, "reference")
            current_features = self.convert_to_hybrid_features(features2, "current")

            if len(reference_features) < 3:
                results['error'] = f"Insufficient reference features: {len(reference_features)} < 3"
                return results

            if len(current_features) < 3:
                results['error'] = f"Insufficient current features: {len(current_features)} < 3"
                return results

            self.progress_update.emit(f"Created {len(reference_features)} reference and {len(current_features)} current HybridFeatures")

            # Create geometric hasher and generate hash table from reference features
            self.progress_update.emit("Building geometric hash table from reference features...")
            from hybrid_corner_alignment import GeometricHasher
            # Use very low precision for real ORB keypoints (coordinate noise requires tolerance)
            geometric_hasher = GeometricHasher(hash_precision=0, debug=True, match_threshold=0.5)
            hash_table = geometric_hasher.generate_hash_table(reference_features)

            if not hash_table:
                results['error'] = "Failed to build hash table from reference features"
                return results

            self.progress_update.emit(f"Built hash table with {len(hash_table)} buckets")

            # Find feature matches using geometric hashing
            self.progress_update.emit("Finding feature matches using geometric hashing...")
            matches = geometric_hasher.find_matches(current_features, min_matches=3)

            if len(matches) < 3:
                results['error'] = f"Insufficient feature matches: {len(matches)} < 3 (need at least 3 for transformation)"
                return results

            self.progress_update.emit(f"Found {len(matches)} feature matches")

            # Calculate transformation matrix using the real method
            self.progress_update.emit("Calculating transformation matrix...")

            # Create mock stage controller and region if alignment_system doesn't exist
            if not hasattr(self, 'alignment_system') or self.alignment_system is None:
                class MockStageController:
                    def move_absolute(self, x, y): pass
                    def get_position(self): return (0, 0)
                    def set_zero(self): pass

                mock_stage = MockStageController()
                mock_region = {'top': 0, 'left': 0, 'width': 800, 'height': 600}
                self.alignment_system = HybridCornerAlignmentSystem(mock_stage, mock_region)

            transformation_result = self.alignment_system._calculate_transformation_matrix(matches)

            if not transformation_result['success']:
                results['error'] = transformation_result.get('error', 'Transformation calculation failed')
                return results

            # Extract transformation parameters from affine matrix
            matrix = np.array(transformation_result['matrix'])
            translation = [matrix[0, 2], matrix[1, 2]]  # tx, ty from affine matrix

            # Calculate rotation from affine matrix
            # For affine matrix [[a, b, tx], [c, d, ty]], rotation = atan2(b, a)
            rotation_radians = np.arctan2(matrix[1, 0], matrix[0, 0])
            rotation_degrees = np.degrees(rotation_radians)

            # Calculate scale factors
            scale_x = np.sqrt(matrix[0, 0]**2 + matrix[1, 0]**2)
            scale_y = np.sqrt(matrix[0, 1]**2 + matrix[1, 1]**2)

            # Calculate confidence based on match quality
            match_qualities = [match['match_quality'] for match in matches]
            confidence = np.mean(match_qualities) if match_qualities else 0.0

            results.update({
                'success': True,
                'transformation': {
                    'translation': translation,
                    'rotation_degrees': rotation_degrees,
                    'rotation_radians': rotation_radians,
                    'scale_x': scale_x,
                    'scale_y': scale_y
                },
                'confidence': confidence,
                'matched_features': len(matches),
                'matrix': transformation_result['matrix'],
                'matches': matches[:5],  # Store top 5 matches for analysis
                'src_points': transformation_result.get('src_points', []),
                'dst_points': transformation_result.get('dst_points', [])
            })

            self.progress_update.emit(f"✓ Transformation calculated: translation=({translation[0]:.2f}, {translation[1]:.2f}), rotation={rotation_degrees:.3f}°")

        except Exception as e:
            results['error'] = f"Transformation calculation error: {str(e)}"
            self.progress_update.emit(f"❌ Transformation calculation failed: {str(e)}")

        results['processing_time'] = time.time() - start_time
        return results

    def convert_to_hybrid_features(self, features_dict, source_name):
        """Convert detected features to HybridFeature objects"""
        hybrid_features = []

        try:
            # Convert CNN-detected flakes to HybridFeature objects
            for i, flake in enumerate(features_dict['flakes']):
                hybrid_feature = HybridFeature(
                    id=f"{source_name}_flake_{i}",
                    feature_type="flake",
                    x_um=flake['center_x'],  # Using pixel coordinates as micrometers for now
                    y_um=flake['center_y'],
                    pixel_x=flake['center_x'],
                    pixel_y=flake['center_y'],
                    confidence=flake['confidence'],
                    flake_data={
                        'class_id': flake['class_id'],
                        'bbox': flake['bbox'],
                        'area': (flake['bbox'][2] - flake['bbox'][0]) * (flake['bbox'][3] - flake['bbox'][1])
                    }
                )
                hybrid_features.append(hybrid_feature)

            # Convert ORB edge keypoints to HybridFeature objects
            for i, keypoint in enumerate(features_dict['edge_keypoints']):
                hybrid_feature = HybridFeature(
                    id=f"{source_name}_keypoint_{i}",
                    feature_type="edge_keypoint",
                    x_um=keypoint['x'],  # Using pixel coordinates as micrometers for now
                    y_um=keypoint['y'],
                    pixel_x=keypoint['x'],
                    pixel_y=keypoint['y'],
                    confidence=min(keypoint['response'] / 100.0, 1.0),  # Normalize response to 0-1
                    keypoint_data={
                        'response': keypoint['response'],
                        'angle': keypoint['angle'],
                        'octave': keypoint['octave'],
                        'size': keypoint['size']
                    }
                )
                hybrid_features.append(hybrid_feature)

            self.progress_update.emit(f"Converted {len(hybrid_features)} features ({len(features_dict['flakes'])} flakes, {len(features_dict['edge_keypoints'])} keypoints)")

        except Exception as e:
            self.progress_update.emit(f"Error converting features: {str(e)}")
            raise

        return hybrid_features

    def calculate_feature_centroid(self, features):
        """Calculate centroid of all features"""
        all_x = []
        all_y = []
        
        # Add flake centers
        for flake in features['flakes']:
            all_x.append(flake['center_x'])
            all_y.append(flake['center_y'])
        
        # Add keypoint positions
        for kp in features['edge_keypoints']:
            all_x.append(kp['x'])
            all_y.append(kp['y'])
        
        if all_x and all_y:
            return [np.mean(all_x), np.mean(all_y)]
        return [0, 0]
    
    def create_comparison_visualization(self, image1, image2, features1, features2, alignment_results):
        """Create side-by-side comparison visualization with translation indicators"""
        try:
            # Get annotated images
            annotated1 = features1.get('annotated_image', image1)
            annotated2 = features2.get('annotated_image', image2)

            # Resize images to same height
            h1, w1 = annotated1.shape[:2]
            h2, w2 = annotated2.shape[:2]
            target_height = min(h1, h2, 600)  # Limit height for display

            scale1 = target_height / h1
            scale2 = target_height / h2

            resized1 = cv2.resize(annotated1, (int(w1 * scale1), target_height))
            resized2 = cv2.resize(annotated2, (int(w2 * scale2), target_height))

            # Add translation visualization if alignment succeeded
            if alignment_results['success']:
                resized1, resized2 = self._add_translation_visualization(
                    resized1, resized2, alignment_results, scale1, scale2
                )

            # Create side-by-side comparison
            comparison = np.hstack([resized1, resized2])

            # Add text overlay with results
            if alignment_results['success']:
                trans = alignment_results['transformation']
                text_lines = [
                    f"HYBRID ALIGNMENT RESULTS WITH TRANSLATION VISUALIZATION:",
                    f"Translation: ({trans['translation'][0]:.1f}, {trans['translation'][1]:.1f}) pixels",
                    f"Rotation: {trans['rotation_degrees']:.2f}° | Scale: ({trans['scale_x']:.3f}, {trans['scale_y']:.3f})",
                    f"Confidence: {alignment_results['confidence']:.3f} | Matches: {alignment_results['matched_features']}",
                    f"Point Correspondences: {alignment_results.get('total_correspondences', 'N/A')} | Inliers: {alignment_results.get('inlier_count', 'N/A')}"
                ]
            else:
                text_lines = [f"Alignment Failed: {alignment_results.get('error', 'Unknown error')}"]

            # Draw text overlay
            y_offset = 30
            for line in text_lines:
                cv2.putText(comparison, line, (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                cv2.putText(comparison, line, (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
                y_offset += 22

            return comparison

        except Exception as e:
            self.progress_update.emit(f"Error creating visualization: {str(e)}")
            return None

    def _add_translation_visualization(self, image1, image2, alignment_results, scale1, scale2):
        """Add translation visualization overlays to both images"""
        try:
            # Get transformation data
            trans = alignment_results['transformation']
            translation = trans['translation']
            src_points = alignment_results.get('src_points', [])
            dst_points = alignment_results.get('dst_points', [])

            # Create copies to avoid modifying originals
            vis_image1 = image1.copy()
            vis_image2 = image2.copy()

            h2 = vis_image2.shape[0]

            # Add coordinate grid overlay
            vis_image1 = self._add_coordinate_grid(vis_image1, "Reference Image")
            vis_image2 = self._add_coordinate_grid(vis_image2, "Current Image")

            # Add translation vector visualization
            if src_points and dst_points:
                vis_image1, vis_image2 = self._add_translation_arrows(
                    vis_image1, vis_image2, src_points, dst_points, scale1, scale2
                )

            # Add reference points markers
            if src_points and dst_points:
                vis_image1 = self._add_reference_points(vis_image1, dst_points, scale1, "Reference")
                vis_image2 = self._add_reference_points(vis_image2, src_points, scale2, "Current")

            # Add translation magnitude indicator
            magnitude = np.sqrt(translation[0]**2 + translation[1]**2)
            direction = np.degrees(np.arctan2(translation[1], translation[0]))

            # Add translation info overlay
            trans_info = [
                f"Translation Vector:",
                f"  Magnitude: {magnitude:.1f} pixels",
                f"  Direction: {direction:.1f}°",
                f"  X-shift: {translation[0]:.1f} ({'→' if translation[0] > 0 else '←' if translation[0] < 0 else '•'})",
                f"  Y-shift: {translation[1]:.1f} ({'↓' if translation[1] > 0 else '↑' if translation[1] < 0 else '•'})"
            ]

            # Draw translation info on image2 (bottom area)
            y_start = h2 - len(trans_info) * 20 - 10
            for i, info in enumerate(trans_info):
                y_pos = y_start + i * 18
                cv2.putText(vis_image2, info, (10, y_pos),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
                cv2.putText(vis_image2, info, (10, y_pos),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

            return vis_image1, vis_image2

        except Exception as e:
            self.progress_update.emit(f"Error adding translation visualization: {str(e)}")
            return image1, image2

    def _add_coordinate_grid(self, image, title):
        """Add coordinate grid overlay to help visualize transformations"""
        h, w = image.shape[:2]
        overlay = image.copy()

        # Draw grid lines every 50 pixels
        grid_spacing = 50
        grid_color = (128, 128, 128)

        # Vertical lines
        for x in range(0, w, grid_spacing):
            cv2.line(overlay, (x, 0), (x, h), grid_color, 1)

        # Horizontal lines
        for y in range(0, h, grid_spacing):
            cv2.line(overlay, (0, y), (w, y), grid_color, 1)

        # Blend with original image
        alpha = 0.3
        result = cv2.addWeighted(image, 1-alpha, overlay, alpha, 0)

        # Add title
        cv2.putText(result, title, (10, 25),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        cv2.putText(result, title, (10, 25),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 1)

        return result

    def _add_translation_arrows(self, image1, image2, src_points, dst_points, scale1, scale2):
        """Add translation arrows showing point correspondences"""
        # Scale points to image coordinates
        scaled_src = [(int(p[0] * scale2), int(p[1] * scale2)) for p in src_points[:5]]  # Limit to 5 for clarity
        scaled_dst = [(int(p[0] * scale1), int(p[1] * scale1)) for p in dst_points[:5]]

        # Draw correspondence arrows on both images
        arrow_colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255)]

        for i, ((sx, sy), (dx, dy)) in enumerate(zip(scaled_src, scaled_dst)):
            color = arrow_colors[i % len(arrow_colors)]

            # Draw source point and arrow on image2 (current)
            cv2.circle(image2, (sx, sy), 8, color, 2)
            cv2.circle(image2, (sx, sy), 3, (255, 255, 255), -1)

            # Draw destination point on image1 (reference)
            cv2.circle(image1, (dx, dy), 8, color, 2)
            cv2.circle(image1, (dx, dy), 3, (255, 255, 255), -1)

            # Add point labels
            cv2.putText(image1, f"R{i+1}", (dx+10, dy-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            cv2.putText(image2, f"C{i+1}", (sx+10, sy-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

        return image1, image2

    def _add_reference_points(self, image, points, scale, label_prefix):
        """Add reference point markers"""
        for i, (x, y) in enumerate(points[:10]):  # Limit to 10 points
            scaled_x, scaled_y = int(x * scale), int(y * scale)

            # Draw point marker
            cv2.circle(image, (scaled_x, scaled_y), 6, (0, 255, 255), 2)
            cv2.circle(image, (scaled_x, scaled_y), 2, (255, 255, 255), -1)

            # Add small label
            if i < 5:  # Only label first 5 to avoid clutter
                cv2.putText(image, f"{label_prefix[0]}{i+1}",
                           (scaled_x+8, scaled_y-8),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

        return image


class MockStageController:
    """Mock stage controller for testing purposes"""
    
    def __init__(self):
        self.position = [0.0, 0.0]
    
    def get_position(self):
        return self.position
    
    def move_absolute(self, x, y):
        self.position = [x, y]
    
    def set_zero(self):
        self.position = [0.0, 0.0]
    
    def close(self):
        pass


def capture_screenshot():
    """Capture image using direct camera API access"""
    print("Capturing current view...")

    # Use the CentralizedCameraManager for coordinated camera access
    # Skip initial camera test to avoid hanging
    camera_manager = CentralizedCameraManager(camera_index=0)
    img = camera_manager.get_image()
    # Camera disconnection handled by CentralizedCameraManager
    print(f"Captured image from camera, shape: {img.shape}")
    return img


def load_image_from_file(filepath):
    """Load and validate image from file with Unicode path support"""
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"Image file not found: {filepath}")
    
    try:
        # Method 1: Try direct OpenCV imread first
        img = cv2.imread(filepath)
        if img is not None:
            print(f"Loaded image from: {filepath}")
            print(f"Image shape: {img.shape}")
            return img
    except:
        pass
    
    try:
        # Method 2: Use numpy and cv2.imdecode for Unicode paths
        with open(filepath, 'rb') as f:
            file_bytes = f.read()
        
        nparr = np.frombuffer(file_bytes, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if img is not None:
            print(f"Loaded image from: {filepath} (using Unicode-safe method)")
            print(f"Image shape: {img.shape}")
            return img
    except Exception as e:
        print(f"Unicode method failed: {e}")
    
    raise ValueError(f"Could not load image from: {filepath}")


def create_test_image(output_path="test_hybrid_alignment.png"):
    """Create a test image with simulated chip and flakes for testing"""
    try:
        # Create a test image with a simulated chip
        img = np.ones((600, 800, 3), dtype=np.uint8) * 200  # Light background

        # Add a darker 'chip' area
        cv2.rectangle(img, (150, 100), (650, 500), (80, 80, 80), -1)

        # Add some 'flakes' on the chip
        cv2.circle(img, (300, 250), 20, (50, 50, 50), -1)
        cv2.circle(img, (500, 350), 15, (60, 60, 60), -1)
        cv2.ellipse(img, (400, 200), (25, 15), 30, 0, 360, (40, 40, 40), -1)
        cv2.circle(img, (250, 400), 18, (45, 45, 45), -1)
        cv2.ellipse(img, (550, 180), (20, 12), -20, 0, 360, (55, 55, 55), -1)

        # Add some edge features (notches, jags)
        # Left edge notch
        cv2.rectangle(img, (145, 300), (155, 320), (200, 200, 200), -1)
        # Top edge jag
        points = np.array([[400, 95], [410, 105], [420, 95]], np.int32)
        cv2.fillPoly(img, [points], (200, 200, 200))

        # Add background texture
        noise = np.random.normal(0, 5, img.shape).astype(np.uint8)
        img = cv2.add(img, noise)

        success = cv2.imwrite(output_path, img)
        if success:
            print(f"Created test image: {output_path}")
            return output_path
        else:
            raise ValueError("Failed to create test image")

    except Exception as e:
        print(f"Error creating test image: {e}")
        return None


class HybridAlignmentVerificationGUI(QMainWindow):
    """Main GUI application for hybrid corner alignment verification"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Hybrid Corner Alignment Verification Tool")
        self.setGeometry(100, 100, 1400, 900)

        # Data storage
        self.image1 = None
        self.image2 = None
        self.image1_source = ""
        self.image2_source = ""
        self.current_results = None
        self.processing_worker = None

        # Setup UI
        self.setup_ui()
        self.setup_styling()

        # Status
        self.statusBar().showMessage("Ready - Load two images to begin hybrid alignment verification")

    def setup_ui(self):
        """Setup the main user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)

        # Title
        title_label = QLabel("Hybrid Corner Alignment Verification Tool")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)

        # Create splitter for main content
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)

        # Left panel - Controls
        self.setup_control_panel(splitter)

        # Right panel - Results and visualization
        self.setup_results_panel(splitter)

        # Set splitter proportions
        splitter.setSizes([400, 1000])

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

        # Status text area
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setPlaceholderText("Status messages will appear here...")
        main_layout.addWidget(self.status_text)

    def setup_control_panel(self, parent):
        """Setup the left control panel"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)

        # Image 1 input group
        image1_group = QGroupBox("Reference Image (Image 1)")
        image1_layout = QVBoxLayout(image1_group)

        self.image1_label = QLabel("No image loaded")
        self.image1_label.setStyleSheet("border: 1px solid gray; padding: 10px; min-height: 60px;")
        image1_layout.addWidget(self.image1_label)

        image1_buttons = QHBoxLayout()
        self.capture1_btn = QPushButton("📷 Capture Live")
        self.load1_btn = QPushButton("📁 Load File")
        self.test1_btn = QPushButton("🧪 Create Test")

        self.capture1_btn.clicked.connect(lambda: self.capture_image(1))
        self.load1_btn.clicked.connect(lambda: self.load_image_file(1))
        self.test1_btn.clicked.connect(lambda: self.create_test_image_for_slot(1))

        image1_buttons.addWidget(self.capture1_btn)
        image1_buttons.addWidget(self.load1_btn)
        image1_buttons.addWidget(self.test1_btn)
        image1_layout.addLayout(image1_buttons)

        control_layout.addWidget(image1_group)

        # Image 2 input group
        image2_group = QGroupBox("Current Image (Image 2)")
        image2_layout = QVBoxLayout(image2_group)

        self.image2_label = QLabel("No image loaded")
        self.image2_label.setStyleSheet("border: 1px solid gray; padding: 10px; min-height: 60px;")
        image2_layout.addWidget(self.image2_label)

        image2_buttons = QHBoxLayout()
        self.capture2_btn = QPushButton("📷 Capture Live")
        self.load2_btn = QPushButton("📁 Load File")
        self.test2_btn = QPushButton("🧪 Create Test")

        self.capture2_btn.clicked.connect(lambda: self.capture_image(2))
        self.load2_btn.clicked.connect(lambda: self.load_image_file(2))
        self.test2_btn.clicked.connect(lambda: self.create_test_image_for_slot(2))

        image2_buttons.addWidget(self.capture2_btn)
        image2_buttons.addWidget(self.load2_btn)
        image2_buttons.addWidget(self.test2_btn)
        image2_layout.addLayout(image2_buttons)

        control_layout.addWidget(image2_group)

        # Processing controls
        process_group = QGroupBox("Analysis Controls")
        process_layout = QVBoxLayout(process_group)

        self.analyze_btn = QPushButton("🔍 Analyze Hybrid Alignment")
        self.analyze_btn.setEnabled(False)
        self.analyze_btn.clicked.connect(self.start_analysis)
        process_layout.addWidget(self.analyze_btn)

        self.export_btn = QPushButton("💾 Export Results")
        self.export_btn.setEnabled(False)
        self.export_btn.clicked.connect(self.export_results)
        process_layout.addWidget(self.export_btn)

        control_layout.addWidget(process_group)

        # Add stretch to push everything to top
        control_layout.addStretch()

        parent.addWidget(control_widget)

    def setup_results_panel(self, parent):
        """Setup the right results panel"""
        results_widget = QWidget()
        results_layout = QVBoxLayout(results_widget)

        # Visualization area
        viz_group = QGroupBox("Feature Detection & Alignment Visualization")
        viz_layout = QVBoxLayout(viz_group)

        # Scroll area for large images
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumHeight(400)

        self.visualization_label = QLabel("Load two images and click 'Analyze' to see visualization")
        self.visualization_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.visualization_label.setStyleSheet("border: 1px solid gray; padding: 20px; background-color: #f0f0f0;")
        scroll_area.setWidget(self.visualization_label)

        viz_layout.addWidget(scroll_area)
        results_layout.addWidget(viz_group)

        # Results display
        results_group = QGroupBox("Detailed Results")
        results_layout_inner = QVBoxLayout(results_group)

        self.results_text = QTextEdit()
        self.results_text.setPlaceholderText("Analysis results will appear here...")
        self.results_text.setMaximumHeight(200)
        results_layout_inner.addWidget(self.results_text)

        results_layout.addWidget(results_group)

        parent.addWidget(results_widget)

    def setup_styling(self):
        """Apply consistent styling to the application"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                padding: 8px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: #f0f0f0;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
            QPushButton:disabled {
                background-color: #f5f5f5;
                color: #999999;
            }
        """)

    def capture_image(self, slot):
        """Capture image from live camera view"""
        try:
            self.add_status_message(f"Capturing image for slot {slot}...")
            image = capture_screenshot()

            if slot == 1:
                self.image1 = image
                self.image1_source = "Live Camera Capture"
                self.image1_label.setText(f"✓ Captured: {image.shape[1]}x{image.shape[0]} pixels")
            else:
                self.image2 = image
                self.image2_source = "Live Camera Capture"
                self.image2_label.setText(f"✓ Captured: {image.shape[1]}x{image.shape[0]} pixels")

            self.update_analyze_button_state()
            self.add_status_message(f"✓ Image {slot} captured successfully")

        except Exception as e:
            self.add_status_message(f"❌ Error capturing image {slot}: {str(e)}")
            QMessageBox.warning(self, "Capture Error", f"Failed to capture image: {str(e)}")

    def load_image_file(self, slot):
        """Load image from file"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                f"Select Image {slot}",
                "",
                "Image Files (*.png *.jpg *.jpeg *.bmp *.tiff *.tif);;All Files (*)"
            )

            if file_path:
                self.add_status_message(f"Loading image {slot} from file...")
                image = load_image_from_file(file_path)

                if slot == 1:
                    self.image1 = image
                    self.image1_source = f"File: {os.path.basename(file_path)}"
                    self.image1_label.setText(f"✓ Loaded: {os.path.basename(file_path)} ({image.shape[1]}x{image.shape[0]})")
                else:
                    self.image2 = image
                    self.image2_source = f"File: {os.path.basename(file_path)}"
                    self.image2_label.setText(f"✓ Loaded: {os.path.basename(file_path)} ({image.shape[1]}x{image.shape[0]})")

                self.update_analyze_button_state()
                self.add_status_message(f"✓ Image {slot} loaded successfully")

        except Exception as e:
            self.add_status_message(f"❌ Error loading image {slot}: {str(e)}")
            QMessageBox.warning(self, "Load Error", f"Failed to load image: {str(e)}")

    def create_test_image_for_slot(self, slot):
        """Create a test image for the specified slot"""
        try:
            self.add_status_message(f"Creating test image for slot {slot}...")

            # Create unique test image with slight variations
            output_path = f"test_hybrid_alignment_{slot}.png"

            # Create base test image
            img = np.ones((600, 800, 3), dtype=np.uint8) * 200
            cv2.rectangle(img, (150, 100), (650, 500), (80, 80, 80), -1)

            # Add different features for each slot to simulate movement/rotation
            if slot == 1:
                # Reference image - original positions
                cv2.circle(img, (300, 250), 20, (50, 50, 50), -1)
                cv2.circle(img, (500, 350), 15, (60, 60, 60), -1)
                cv2.ellipse(img, (400, 200), (25, 15), 30, 0, 360, (40, 40, 40), -1)
            else:
                # Current image - slightly shifted positions to simulate stage movement
                cv2.circle(img, (305, 255), 20, (50, 50, 50), -1)  # Shifted +5, +5
                cv2.circle(img, (503, 348), 15, (60, 60, 60), -1)   # Shifted +3, -2
                cv2.ellipse(img, (398, 205), (25, 15), 25, 0, 360, (40, 40, 40), -1)  # Shifted -2, +5, rotated

            # Add edge features
            cv2.rectangle(img, (145, 300), (155, 320), (200, 200, 200), -1)
            points = np.array([[400, 95], [410, 105], [420, 95]], np.int32)
            cv2.fillPoly(img, [points], (200, 200, 200))

            # Add noise
            noise = np.random.normal(0, 5, img.shape).astype(np.uint8)
            img = cv2.add(img, noise)

            success = cv2.imwrite(output_path, img)
            if success:
                if slot == 1:
                    self.image1 = img
                    self.image1_source = f"Generated Test Image {slot}"
                    self.image1_label.setText(f"✓ Test Image {slot}: {img.shape[1]}x{img.shape[0]} pixels")
                else:
                    self.image2 = img
                    self.image2_source = f"Generated Test Image {slot}"
                    self.image2_label.setText(f"✓ Test Image {slot}: {img.shape[1]}x{img.shape[0]} pixels")

                self.update_analyze_button_state()
                self.add_status_message(f"✓ Test image {slot} created successfully")
            else:
                raise ValueError("Failed to save test image")

        except Exception as e:
            self.add_status_message(f"❌ Error creating test image {slot}: {str(e)}")
            QMessageBox.warning(self, "Test Image Error", f"Failed to create test image: {str(e)}")

    def update_analyze_button_state(self):
        """Enable/disable analyze button based on image availability"""
        has_both_images = self.image1 is not None and self.image2 is not None
        self.analyze_btn.setEnabled(has_both_images)

        if has_both_images:
            self.statusBar().showMessage("Ready to analyze - Click 'Analyze Hybrid Alignment' to begin")
        else:
            missing = []
            if self.image1 is None:
                missing.append("Image 1")
            if self.image2 is None:
                missing.append("Image 2")
            self.statusBar().showMessage(f"Load {' and '.join(missing)} to enable analysis")

    def add_status_message(self, message):
        """Add a timestamped message to the status text area"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.status_text.append(formatted_message)

        # Auto-scroll to bottom
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.status_text.setTextCursor(cursor)

        # Process events to update UI
        QApplication.processEvents()

    def start_analysis(self):
        """Start the hybrid alignment analysis"""
        if self.image1 is None or self.image2 is None:
            QMessageBox.warning(self, "Missing Images", "Please load both images before starting analysis.")
            return

        if self.processing_worker and self.processing_worker.isRunning():
            QMessageBox.information(self, "Processing", "Analysis is already in progress.")
            return

        # Disable controls during processing
        self.analyze_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress

        # Clear previous results
        self.results_text.clear()
        self.visualization_label.setText("Processing...")

        # Start processing worker
        self.processing_worker = ImageProcessingWorker(
            self.image1, self.image2, self.image1_source, self.image2_source
        )

        # Connect signals
        self.processing_worker.progress_update.connect(self.add_status_message)
        self.processing_worker.feature_detection_complete.connect(self.on_feature_detection_complete)
        self.processing_worker.alignment_complete.connect(self.on_alignment_complete)
        self.processing_worker.error_occurred.connect(self.on_processing_error)
        self.processing_worker.finished.connect(self.on_processing_finished)

        # Start processing
        self.processing_worker.start()
        self.add_status_message("🚀 Starting hybrid corner alignment analysis...")

    def on_feature_detection_complete(self, results):
        """Handle feature detection completion"""
        try:
            features1 = results['image1_features']
            features2 = results['image2_features']

            # Update results display
            feature_summary = f"""Feature Detection Results:

Image 1 ({results['image1_source']}):
  • CNN Flakes: {len(features1['flakes'])}
  • ORB Edge Keypoints: {len(features1['edge_keypoints'])}
  • Total Features: {features1['total_features']}
  • Processing Time: {features1['processing_time']:.2f}s

Image 2 ({results['image2_source']}):
  • CNN Flakes: {len(features2['flakes'])}
  • ORB Edge Keypoints: {len(features2['edge_keypoints'])}
  • Total Features: {features2['total_features']}
  • Processing Time: {features2['processing_time']:.2f}s

Calculating transformation matrix..."""

            self.results_text.setText(feature_summary)

        except Exception as e:
            self.add_status_message(f"❌ Error processing feature detection results: {str(e)}")

    def on_alignment_complete(self, results):
        """Handle alignment calculation completion"""
        try:
            self.current_results = results

            # Update visualization if available
            if results.get('visualization') is not None:
                self.display_visualization(results['visualization'])

            # Update detailed results
            self.display_detailed_results(results)

            # Enable export
            self.export_btn.setEnabled(True)

        except Exception as e:
            self.add_status_message(f"❌ Error processing alignment results: {str(e)}")

    def display_visualization(self, visualization_image):
        """Display the comparison visualization"""
        try:
            # Convert OpenCV image to QPixmap
            height, width = visualization_image.shape[:2]
            bytes_per_line = 3 * width
            q_image = QImage(visualization_image.data, width, height, bytes_per_line, QImage.Format.Format_RGB888).rgbSwapped()
            pixmap = QPixmap.fromImage(q_image)

            # Scale image to fit display while maintaining aspect ratio
            max_width = 1000
            max_height = 600
            scaled_pixmap = pixmap.scaled(max_width, max_height, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)

            self.visualization_label.setPixmap(scaled_pixmap)
            self.visualization_label.setText("")  # Clear text

        except Exception as e:
            self.add_status_message(f"❌ Error displaying visualization: {str(e)}")
            self.visualization_label.setText(f"Visualization Error: {str(e)}")

    def display_detailed_results(self, results):
        """Display detailed alignment results"""
        try:
            if results['success']:
                trans = results['transformation']
                detailed_results = f"""Hybrid Corner Alignment Results:

✓ ALIGNMENT SUCCESSFUL (Using Real HybridCornerAlignmentSystem)

Transformation Parameters (from cv2.getAffineTransform):
  • Translation: ({trans['translation'][0]:.2f}, {trans['translation'][1]:.2f}) pixels
  • Rotation: {trans['rotation_degrees']:.3f}° ({trans['rotation_radians']:.6f} radians)
  • Scale X: {trans['scale_x']:.4f}
  • Scale Y: {trans['scale_y']:.4f}
  • Confidence Score: {results['confidence']:.3f}
  • Matched Features: {results['matched_features']}
  • Processing Time: {results['processing_time']:.2f}s

Affine Transformation Matrix:
  • Matrix: {results.get('matrix', 'N/A')}

Quality Assessment:
  • Confidence Level: {'High' if results['confidence'] > 0.8 else 'Medium' if results['confidence'] > 0.6 else 'Low'}
  • Feature Match Count: {'Excellent' if results['matched_features'] > 10 else 'Good' if results['matched_features'] > 5 else 'Adequate' if results['matched_features'] >= 3 else 'Poor'}
  • Scale Uniformity: {'Good' if abs(trans['scale_x'] - trans['scale_y']) < 0.1 else 'Non-uniform scaling detected'}

Translation Analysis:
  • X-axis shift: {abs(trans['translation'][0]):.2f} pixels ({'Right' if trans['translation'][0] > 0 else 'Left' if trans['translation'][0] < 0 else 'No shift'})
  • Y-axis shift: {abs(trans['translation'][1]):.2f} pixels ({'Down' if trans['translation'][1] > 0 else 'Up' if trans['translation'][1] < 0 else 'No shift'})
  • Total displacement: {np.sqrt(trans['translation'][0]**2 + trans['translation'][1]**2):.2f} pixels

Rotation Analysis:
  • Rotation magnitude: {abs(trans['rotation_degrees']):.3f}°
  • Direction: {'Clockwise' if trans['rotation_degrees'] > 0 else 'Counter-clockwise' if trans['rotation_degrees'] < 0 else 'No rotation'}

Geometric Hashing Results:
  • Top matches used for transformation calculation
  • Match quality scores: {[f"{match['match_quality']:.3f}" for match in results.get('matches', [])][:3]}"""
            else:
                detailed_results = f"""Hybrid Corner Alignment Results:

❌ ALIGNMENT FAILED

Error: {results.get('error', 'Unknown error occurred')}
Processing Time: {results['processing_time']:.2f}s

Troubleshooting Suggestions:
  • Ensure both images contain sufficient features (flakes and edge details)
  • Check that images show overlapping regions of the same chip
  • Verify image quality and focus
  • Try different images with more distinctive features"""

            self.results_text.setText(detailed_results)

        except Exception as e:
            self.add_status_message(f"❌ Error displaying results: {str(e)}")
            self.results_text.setText(f"Results Display Error: {str(e)}")

    def on_processing_error(self, error_message):
        """Handle processing errors"""
        self.add_status_message(f"❌ Processing error: {error_message}")
        QMessageBox.critical(self, "Processing Error", f"An error occurred during processing:\n\n{error_message}")

    def on_processing_finished(self):
        """Handle processing completion"""
        # Re-enable controls
        self.analyze_btn.setEnabled(True)
        self.progress_bar.setVisible(False)

        # Update status
        if self.current_results and self.current_results.get('success'):
            self.statusBar().showMessage("✓ Analysis completed successfully - Results available for export")
        else:
            self.statusBar().showMessage("❌ Analysis completed with errors - Check results for details")

    def export_results(self):
        """Export analysis results and visualization"""
        if not self.current_results:
            QMessageBox.warning(self, "No Results", "No analysis results available to export.")
            return

        try:
            # Get export directory
            export_dir = QFileDialog.getExistingDirectory(self, "Select Export Directory")
            if not export_dir:
                return

            timestamp = time.strftime("%Y%m%d_%H%M%S")
            base_name = f"hybrid_alignment_verification_{timestamp}"

            # Export visualization image
            if self.current_results.get('visualization') is not None:
                viz_path = os.path.join(export_dir, f"{base_name}_visualization.png")
                cv2.imwrite(viz_path, self.current_results['visualization'])
                self.add_status_message(f"✓ Exported visualization: {viz_path}")

            # Export detailed results as JSON
            results_data = {
                'timestamp': timestamp,
                'image1_source': self.image1_source,
                'image2_source': self.image2_source,
                'alignment_results': self.current_results,
                'analysis_summary': self.results_text.toPlainText()
            }

            json_path = os.path.join(export_dir, f"{base_name}_results.json")
            with open(json_path, 'w') as f:
                json.dump(results_data, f, indent=2, default=str)
            self.add_status_message(f"✓ Exported results: {json_path}")

            # Export text summary
            txt_path = os.path.join(export_dir, f"{base_name}_summary.txt")
            with open(txt_path, 'w') as f:
                f.write(f"Hybrid Corner Alignment Verification Results\n")
                f.write(f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"="*60 + "\n\n")
                f.write(self.results_text.toPlainText())
            self.add_status_message(f"✓ Exported summary: {txt_path}")

            QMessageBox.information(self, "Export Complete",
                                  f"Results exported successfully to:\n{export_dir}")

        except Exception as e:
            self.add_status_message(f"❌ Export error: {str(e)}")
            QMessageBox.critical(self, "Export Error", f"Failed to export results:\n\n{str(e)}")


def main():
    """Main application entry point"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Hybrid Corner Alignment Verification Tool")
    parser.add_argument("--image1", help="Path to first image file")
    parser.add_argument("--image2", help="Path to second image file")
    parser.add_argument("--auto-analyze", action="store_true", help="Automatically start analysis if both images provided")
    args = parser.parse_args()

    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("Hybrid Corner Alignment Verification")
    app.setApplicationVersion("1.0")

    # Create and show main window
    window = HybridAlignmentVerificationGUI()
    window.show()

    # Load images from command line if provided
    if args.image1:
        try:
            image1 = load_image_from_file(args.image1)
            window.image1 = image1
            window.image1_source = f"CLI: {os.path.basename(args.image1)}"
            window.image1_label.setText(f"✓ CLI Loaded: {os.path.basename(args.image1)}")
            window.add_status_message(f"✓ Loaded image 1 from command line: {args.image1}")
        except Exception as e:
            window.add_status_message(f"❌ Failed to load image 1 from CLI: {str(e)}")

    if args.image2:
        try:
            image2 = load_image_from_file(args.image2)
            window.image2 = image2
            window.image2_source = f"CLI: {os.path.basename(args.image2)}"
            window.image2_label.setText(f"✓ CLI Loaded: {os.path.basename(args.image2)}")
            window.add_status_message(f"✓ Loaded image 2 from command line: {args.image2}")
        except Exception as e:
            window.add_status_message(f"❌ Failed to load image 2 from CLI: {str(e)}")

    # Update button states
    window.update_analyze_button_state()

    # Auto-analyze if requested and both images loaded
    if args.auto_analyze and window.image1 is not None and window.image2 is not None:
        QTimer.singleShot(1000, window.start_analysis)  # Delay to allow UI to fully load

    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
