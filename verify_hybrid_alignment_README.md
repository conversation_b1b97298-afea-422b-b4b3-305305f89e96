# Hybrid Corner Alignment Verification Tool

## Overview

The `verify_hybrid_alignment.py` script is a comprehensive verification tool for the hybrid corner alignment system. It provides a PyQt6-based GUI application that follows the design patterns established in `verify_edge_detection.py` and enables thorough testing of the hybrid corner feature hashing system.

## Key Features

### 🔍 **Dual Image Input System**
- **Live Camera Capture**: Capture images directly from the microscope camera view
- **File Selection**: Load saved images from disk with Unicode path support
- **Test Image Generation**: Create synthetic test images with simulated chip features

### 🧠 **Hybrid Feature Detection**
- **CNN-based Flake Detection**: Uses the existing Roboflow inference system to detect flakes
- **ORB Edge Keypoint Detection**: Detects stable keypoints on chip edges using OpenCV ORB
- **Feature Visualization**: Displays detected features with color-coded annotations

### 📊 **Transformation Matrix Calculation**
- **Geometric Hashing**: Applies triangle-based feature matching for rotation/translation invariance
- **Confidence Scoring**: Provides reliability metrics for alignment calculations
- **Detailed Analysis**: Shows translation, rotation, and quality assessment

### 🎨 **Comprehensive Visualization**
- **Side-by-Side Comparison**: Shows both images with feature overlays
- **Real-time Processing**: Threaded processing with progress indicators
- **Export Functionality**: Save results as images, JSON data, and text summaries

## Architecture

### GUI Components

The application follows PyQt6 design patterns with:

- **Control Panel**: Left sidebar with image input controls and analysis buttons
- **Results Panel**: Right panel showing visualization and detailed results
- **Status System**: Real-time status updates and progress tracking
- **Export System**: Comprehensive result export in multiple formats

### Processing Pipeline

1. **Image Acquisition**: Capture or load two images for comparison
2. **Feature Detection**: Run CNN flake detection and ORB keypoint detection on both images
3. **Feature Matching**: Use geometric hashing to match features between images
4. **Transformation Calculation**: Calculate translation and rotation parameters
5. **Visualization**: Create annotated comparison images
6. **Results Display**: Show detailed analysis and quality metrics

## Usage

### Command Line Interface

```bash
# Basic usage - launch GUI
python verify_hybrid_alignment.py

# Load images from command line
python verify_hybrid_alignment.py --image1 reference.png --image2 current.png

# Auto-analyze after loading
python verify_hybrid_alignment.py --image1 ref.png --image2 cur.png --auto-analyze
```

### GUI Workflow

1. **Load Reference Image (Image 1)**:
   - Click "📷 Capture Live" to capture from camera
   - Click "📁 Load File" to select an image file
   - Click "🧪 Create Test" to generate a synthetic test image

2. **Load Current Image (Image 2)**:
   - Use the same methods as above for the second image
   - Test images are automatically varied to simulate stage movement

3. **Run Analysis**:
   - Click "🔍 Analyze Hybrid Alignment" to start processing
   - Monitor progress in the status area
   - View results in the visualization and results panels

4. **Export Results**:
   - Click "💾 Export Results" to save analysis data
   - Choose export directory for visualization images, JSON data, and text summaries

## Technical Details

### Feature Detection

**CNN Flake Detection**:
- Uses Roboflow inference API with model "raman-fgrub/4"
- Filters for selected classes {0, 2, 3}
- Provides bounding boxes, confidence scores, and class IDs
- Converts to HybridFeature objects with flake_data

**ORB Edge Keypoint Detection**:
- Applies Canny edge detection to create edge mask
- Uses ORB detector with optimized parameters for edge features
- Extracts keypoint positions, responses, angles, and scales
- Converts to HybridFeature objects with keypoint_data

### Real Transformation Calculation

**IMPORTANT**: The verification tool now uses the **actual `HybridCornerAlignmentSystem._calculate_transformation_matrix()` method** instead of simplified mock calculations. This ensures the verification tool demonstrates the exact same transformation calculation logic used in production.

**Geometric Hashing Process**:
1. **Hash Table Generation**: Creates rotation/translation-invariant hash keys from feature triplets using triangle geometry
2. **Feature Matching**: Matches current features against reference hash table using geometric similarity
3. **Affine Transformation**: Uses OpenCV's `cv2.getAffineTransform()` to calculate precise transformation matrix
4. **Parameter Extraction**: Extracts translation, rotation, and scaling parameters from the affine matrix

**Optimized Parameters for Real Data**:
- **Hash Precision: 0** - Rounds triangle side lengths to integers, providing tolerance for coordinate noise
- **Match Threshold: 0.5** - Reduced from 0.7 to accommodate real ORB keypoint variations
- **Robust Matching**: Successfully handles detection noise, floating-point coordinates, and small transformations

**Transformation Matrix Calculation**:
- Uses the real `GeometricHasher.generate_hash_table()` method to build reference hash table
- Applies `GeometricHasher.find_matches()` for feature matching with optimized quality thresholds
- Calls `HybridCornerAlignmentSystem._calculate_transformation_matrix()` for precise affine transformation
- Extracts translation (tx, ty), rotation (atan2), and scale factors from the 2x3 affine matrix

### Export Formats

**Visualization Images**: PNG files with side-by-side feature annotations showing real transformation results
**JSON Results**: Complete analysis data including affine transformation matrix and geometric hashing results
**Text Summaries**: Human-readable analysis reports with detailed transformation parameters and quality assessments

## Integration with Real Hybrid Corner Alignment System

The verification tool provides **true integration** with the production hybrid corner alignment system:

- **HybridCornerAlignmentSystem**: Uses the actual core alignment calculation engine with real `_calculate_transformation_matrix()` method
- **GeometricHasher**: Uses the real geometric hashing implementation with `generate_hash_table()` and `find_matches()` methods
- **HybridFeature**: Converts detected features to proper HybridFeature objects with complete flake_data and keypoint_data
- **EdgeKeypointDetector**: ORB-based edge feature detection with real feature extraction
- **Mock Stage Controller**: Minimal testing interface that allows the alignment system to function without hardware

**Key Achievement**: The verification tool now demonstrates the **exact same transformation calculation logic** that the actual re-alignment workflow uses in production, making it a true representation of the system's capabilities rather than a simplified demonstration.

## Error Handling and Troubleshooting

### Common Issues

**Import Errors**: Ensure all required modules are available and circular imports are resolved
**Image Loading Failures**: Check file paths and image format compatibility
**Feature Detection Errors**: Verify API connectivity and image quality
**Processing Timeouts**: Monitor system resources during analysis

### Quality Assessment

The tool provides automatic quality assessment:
- **High Confidence**: >0.8 confidence score with >10 matched features
- **Medium Confidence**: 0.6-0.8 confidence score with 5-10 matched features  
- **Low Confidence**: <0.6 confidence score or <5 matched features

## Dependencies

- **PyQt6**: GUI framework
- **OpenCV**: Image processing and feature detection
- **NumPy**: Numerical computations
- **inference-sdk**: Roboflow API client
- **supervision**: Detection result processing
- **mss**: Screen capture functionality

## Future Enhancements

Potential improvements for the verification tool:

1. **Real-time Analysis**: Live camera feed with continuous alignment monitoring
2. **Batch Processing**: Analyze multiple image pairs automatically
3. **Performance Metrics**: Detailed timing and accuracy statistics
4. **Advanced Visualization**: 3D transformation visualization and feature tracking
5. **Calibration Tools**: Stage-to-pixel calibration and coordinate system validation

## Related Files

- `hybrid_corner_alignment.py`: Core alignment system implementation
- `verify_edge_detection.py`: Reference GUI architecture and patterns
- `scanning.py`: Main scanning system with hybrid alignment integration
- `edge_detection.py`: Edge detection algorithms and utilities
- `config.py`: Configuration constants and parameters

This verification tool serves as both a testing mechanism and a demonstration of the hybrid corner alignment system's capabilities, providing comprehensive validation of the alignment accuracy and reliability.
