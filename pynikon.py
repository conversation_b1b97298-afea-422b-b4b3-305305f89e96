from PyNikonSciCam import NikonCamera
import cv2
import matplotlib.pyplot as plt
import os

# Initialize camera
with NikonCamera(0) as camera:  # ID = 0 for camera. ID = 3 for simulator

    # Capture image
    image = cv2.cvtColor(camera.get_image(), cv2.COLOR_BGR2RGB)
    print(type(image))
    

    # Save image as PNG
    # output_path = "get_image.png"
    # cv2.imwrite(output_path, image)
    # print(f"Image saved to {os.path.abspath(output_path)}")

# Display image using OpenCV
# cv2.imshow('Captured Image', image)
# cv2.waitKey(0)
# cv2.destroyAllWindows()

# # Optionally, display image using matplotlib
# plt.imshow(image)
# plt.show()