import sys
import time
import numpy as np
from PyQt5.QtWidgets import QMainWindow, QLabel, QPushButton, QVBoxLayout, QWidget, QApplication
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QImage, QPixmap
from PyNikonSciCam import NikonCamera, constants as consts, methods

class CameraWorker(QThread):
    """Worker thread to capture images continuously from the camera."""
    imageCaptured = pyqtSignal(np.ndarray)
    errorOccurred = pyqtSignal(str)

    def __init__(self, camera):
        super().__init__()
        self.camera = camera
        self.stopped = False

    def run(self):
        """Start live streaming and capture images in a loop."""
        try:
            self.camera.start_live()
            while not self.stopped:
                img = self.camera.get_live_image()
                if img is not None:
                    self.imageCaptured.emit(img)
                else:
                    time.sleep(0.01)  # Brief sleep if no image is available
        except Exception as e:
            self.errorOccurred.emit(f"Error in worker thread: {str(e)}")
        finally:
            self.camera.stop_live()

    def stop(self):
        """Signal the thread to stop."""
        self.stopped = True

class NikonCameraExtended(NikonCamera):
    """Extended NikonCamera class with live streaming capabilities."""

    def start_live(self):
        """Start live streaming by setting free-run mode and beginning frame transfer."""
        self.set_trigger_mode(consts.ECamTriggerMode.Off)
        self._start_FrameTransfer()

    def stop_live(self):
        """Stop live streaming by ending frame transfer."""
        self._stop_FrameTransfer()

    def get_live_image(self):
        """Get the latest image during live streaming without stopping transfer."""
        if self._stImage is None:
            raise RuntimeError("Image structure not initialized")

        event_or_none = methods.poll_event(self.camera_handle, consts.ECamEventType.ecetImageReceived)
        if event_or_none is not None and event_or_none.eEventType == consts.ECamEventType.ecetImageReceived:
            try:
                methods.get_image(self.camera_handle, self._stImage, b_newest_required=True)
                height, width = self.height, self.width
                expected_size = height * width * 3
                img = np.ctypeslib.as_array(self._stImage.pDataBuffer, shape=(self._stImage.uiDataBufferSize,))
                if img.size < expected_size:
                    raise ValueError("Image buffer is smaller than expected, cannot reshape.")
                img = img[:expected_size].reshape((height, width, 3))[..., ::-1]  # Convert to RGB
                return img.astype(np.uint8)
            except Exception as e:
                raise Exception(f"Error getting live image: {str(e)}")
        return None

class VideoStreamingGUI(QMainWindow):
    """PyQt GUI for video streaming from Nikon DS Fi3 camera."""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Nikon DS Fi3 Video Streaming")
        self.setGeometry(100, 100, 1200, 900)

        # Initialize camera
        self.camera = NikonCameraExtended()

        # Set up worker thread
        self.worker = CameraWorker(self.camera)
        self.worker.imageCaptured.connect(self.display_image)
        self.worker.errorOccurred.connect(self.handle_error)

        # GUI components
        self.video_label = QLabel(self)
        self.video_label.setAlignment(Qt.AlignCenter)

        self.start_button = QPushButton("Start Stream", self)
        self.start_button.clicked.connect(self.start_stream)

        self.stop_button = QPushButton("Stop Stream", self)
        self.stop_button.clicked.connect(self.stop_stream)
        self.stop_button.setEnabled(False)

        # Layout
        layout = QVBoxLayout()
        layout.addWidget(self.video_label)
        layout.addWidget(self.start_button)
        layout.addWidget(self.stop_button)

        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

    def start_stream(self):
        """Start the video stream."""
        self.worker.start()
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)

    def stop_stream(self):
        """Stop the video stream."""
        self.worker.stop()
        self.worker.wait()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

    def display_image(self, img):
        """Display the captured image in the GUI."""
        q_img = QImage(img.data, img.shape[1], img.shape[0], img.strides[0], QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_img)
        self.video_label.setPixmap(pixmap.scaled(self.video_label.size(), Qt.KeepAspectRatio))

    def handle_error(self, error_msg):
        """Handle errors from the worker thread."""
        print(error_msg)  # Replace with better GUI feedback if needed
        self.stop_stream()

    def closeEvent(self, event):
        """Ensure camera is disconnected when closing the window."""
        if self.worker.isRunning():
            self.worker.stop()
            self.worker.wait()
        self.camera.disconnect()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = VideoStreamingGUI()
    window.show()
    sys.exit(app.exec_())