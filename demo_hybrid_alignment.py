#!/usr/bin/env python3
"""
Demo Script for Hybrid Corner Feature Hashing System

This script demonstrates the capabilities of the hybrid corner alignment system,
including feature detection, geometric hashing, and GUI visualization.

Author: Augment Agent
Date: 2025-07-02
"""

import sys
import os
import numpy as np
import cv2
import json
import time
from typing import List, Dict

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from hybrid_corner_alignment import (
    HybridFeature, EdgeKeypointDetector, HybridFeatureDetector,
    GeometricHasher, HybridCornerAlignmentSystem,
    save_hybrid_reference, load_hybrid_reference
)
from hybrid_corner_gui import create_hybrid_debug_application
from scanning import Flake


class MockStageController:
    """Mock stage controller for demo purposes."""
    
    def __init__(self):
        self.position = [100.0, 200.0]
    
    def get_position(self):
        return tuple(self.position)
    
    def move_absolute(self, y_um, x_um):
        self.position = [y_um, x_um]
        print(f"Mock stage moved to: ({y_um:.2f}, {x_um:.2f}) μm")
    
    def set_zero(self):
        print("Mock stage zero reference set")


def create_synthetic_corner_image() -> np.ndarray:
    """Create a synthetic corner image for demonstration."""
    # Create 400x400 image
    image = np.ones((400, 400, 3), dtype=np.uint8) * 50  # Dark background
    
    # Add chip edges (bright lines)
    cv2.line(image, (50, 0), (50, 400), (200, 200, 200), 3)  # Left edge
    cv2.line(image, (0, 50), (400, 50), (200, 200, 200), 3)  # Top edge
    
    # Add some edge features (jags, notches)
    cv2.circle(image, (52, 100), 3, (255, 255, 255), -1)  # Small notch
    cv2.rectangle(image, (48, 150), (54, 160), (255, 255, 255), -1)  # Small jag
    cv2.circle(image, (51, 250), 2, (255, 255, 255), -1)  # Another notch
    
    # Add some flake-like features
    cv2.circle(image, (120, 120), 8, (100, 150, 200), -1)  # Flake 1
    cv2.ellipse(image, (180, 160), (12, 6), 30, 0, 360, (120, 180, 100), -1)  # Flake 2
    cv2.circle(image, (250, 200), 6, (150, 100, 180), -1)  # Flake 3
    cv2.ellipse(image, (300, 140), (10, 8), 45, 0, 360, (180, 120, 150), -1)  # Flake 4
    
    # Add some noise
    noise = np.random.randint(0, 30, image.shape, dtype=np.uint8)
    image = cv2.add(image, noise)
    
    return image


def create_synthetic_flakes() -> List[Flake]:
    """Create synthetic flakes for demonstration."""
    flakes = [
        Flake("demo_f1", 120, 120, 85.0, 95.0, [], "graphene"),
        Flake("demo_f2", 180, 160, 125.0, 135.0, [], "graphene"),
        Flake("demo_f3", 250, 200, 165.0, 175.0, [], "hBN"),
        Flake("demo_f4", 300, 140, 205.0, 115.0, [], "graphene"),
    ]
    return flakes


def demo_feature_detection():
    """Demonstrate hybrid feature detection."""
    print("=== Hybrid Feature Detection Demo ===")
    
    # Create synthetic data
    corner_image = create_synthetic_corner_image()
    corner_flakes = create_synthetic_flakes()
    stage_position = (100.0, 200.0)
    
    # Initialize feature detector
    feature_detector = HybridFeatureDetector(debug=True)
    
    # Detect features
    print("Detecting hybrid features...")
    features = feature_detector.detect_features(corner_image, corner_flakes, stage_position)
    
    print(f"Detected {len(features)} total features:")
    flake_count = sum(1 for f in features if f.feature_type == "flake")
    keypoint_count = sum(1 for f in features if f.feature_type == "edge_keypoint")
    print(f"  - {flake_count} flakes")
    print(f"  - {keypoint_count} edge keypoints")
    
    # Display feature details
    for i, feature in enumerate(features[:5]):  # Show first 5
        print(f"  Feature {i+1}: {feature.feature_type} {feature.id}")
        print(f"    Position: ({feature.x_um:.2f}, {feature.y_um:.2f}) μm")
        print(f"    Pixel: ({feature.pixel_x:.1f}, {feature.pixel_y:.1f})")
        print(f"    Confidence: {feature.confidence:.3f}")
    
    return features, corner_image


def demo_geometric_hashing(features: List[HybridFeature]):
    """Demonstrate geometric hashing."""
    print("\n=== Geometric Hashing Demo ===")
    
    # Initialize hasher
    hasher = GeometricHasher(hash_precision=3, debug=True)
    
    # Generate hash table
    print("Generating geometric hash table...")
    hash_table = hasher.generate_hash_table(features)
    
    print(f"Generated hash table with {len(hash_table)} buckets")
    
    # Show some hash examples
    print("Sample hash keys:")
    for i, (hash_key, triplets) in enumerate(list(hash_table.items())[:3]):
        print(f"  {hash_key}: {len(triplets)} triplet(s)")
    
    # Test matching with slightly transformed features
    print("\nTesting feature matching...")
    
    # Create transformed query features (simulate re-alignment scenario)
    query_features = []
    for feature in features[:6]:  # Use subset
        # Add small translation and noise
        new_feature = HybridFeature(
            id=f"query_{feature.id}",
            feature_type=feature.feature_type,
            x_um=feature.x_um + np.random.uniform(-2, 2),
            y_um=feature.y_um + np.random.uniform(-2, 2),
            pixel_x=feature.pixel_x + np.random.uniform(-5, 5),
            pixel_y=feature.pixel_y + np.random.uniform(-5, 5),
            confidence=feature.confidence * np.random.uniform(0.9, 1.0)
        )
        query_features.append(new_feature)
    
    # Find matches
    matches = hasher.find_matches(query_features, min_matches=3)
    
    print(f"Found {len(matches)} feature matches")
    for i, match in enumerate(matches[:3]):  # Show first 3
        print(f"  Match {i+1}: Quality {match['match_quality']:.3f}")
        print(f"    Hash: {match['hash_key']}")
    
    return hash_table, matches


def demo_reference_creation():
    """Demonstrate reference creation workflow."""
    print("\n=== Reference Creation Demo ===")
    
    # Create mock stage controller
    mock_stage = MockStageController()
    
    # Initialize alignment system
    alignment_system = HybridCornerAlignmentSystem(
        stage_controller=mock_stage,
        region=(0, 0, 400, 400),
        debug=True
    )
    
    # Create synthetic data
    corner_image = create_synthetic_corner_image()
    corner_flakes = create_synthetic_flakes()
    
    # Create reference
    print("Creating hybrid corner reference...")
    result = alignment_system.create_hybrid_reference(
        corner_flakes=corner_flakes,
        corner_image=corner_image,
        metadata={"demo": True, "version": "1.0"}
    )
    
    if result['success']:
        print("✓ Reference creation successful!")
        ref_data = result['reference_data']
        print(f"  Features: {ref_data['feature_counts']}")
        print(f"  Hash buckets: {len(ref_data['hash_table'])}")
        print(f"  Format version: {ref_data['format_version']}")
        
        # Save reference to file
        demo_ref_file = "demo_hybrid_reference.json"
        if save_hybrid_reference(ref_data, demo_ref_file):
            print(f"✓ Reference saved to {demo_ref_file}")
        
        return ref_data, demo_ref_file
    else:
        print(f"✗ Reference creation failed: {result['error']}")
        return None, None


def demo_realignment(reference_data: Dict):
    """Demonstrate re-alignment workflow."""
    print("\n=== Re-alignment Demo ===")
    
    if not reference_data:
        print("No reference data available for re-alignment demo")
        return
    
    # Create mock stage controller
    mock_stage = MockStageController()
    
    # Initialize alignment system
    alignment_system = HybridCornerAlignmentSystem(
        stage_controller=mock_stage,
        region=(0, 0, 400, 400),
        debug=True
    )
    
    # Simulate re-alignment
    print("Performing hybrid corner re-alignment...")
    
    # Mock the re-alignment process (in practice would capture new corner image)
    try:
        # This would normally call perform_hybrid_realignment
        # For demo, create mock result with new transformation format
        mock_result = {
            'success': True,
            'transformation_matrix': {
                'rotation_matrix': [[1.0, 0.0], [0.0, 1.0]],
                'translation': [3.2, -1.8],
                'raw_matrix': [[1.0, 0.0, 3.2], [0.0, 1.0, -1.8]]
            },
            'corner_offset': (3.2, -1.8),
            'feature_matches': 6,
            'match_quality': 0.847
        }
        
        print("✓ Re-alignment successful!")
        print(f"  Feature matches: {mock_result['feature_matches']}")
        print(f"  Match quality: {mock_result['match_quality']:.3f}")
        print(f"  Corner offset: ({mock_result['corner_offset'][0]:.2f}, {mock_result['corner_offset'][1]:.2f}) μm")
        print(f"  Transformation matrix:")
        matrix = mock_result['transformation_matrix']['raw_matrix']
        print(f"    [{matrix[0][0]:6.3f} {matrix[0][1]:6.3f} {matrix[0][2]:6.3f}]")
        print(f"    [{matrix[1][0]:6.3f} {matrix[1][1]:6.3f} {matrix[1][2]:6.3f}]")
        
        return mock_result
        
    except Exception as e:
        print(f"✗ Re-alignment failed: {str(e)}")
        return None


def demo_gui():
    """Demonstrate GUI components."""
    print("\n=== GUI Demo ===")
    print("Launching hybrid corner alignment debug panel...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        
        # Check if QApplication already exists
        app = QApplication.instance()
        if app is None:
            app, window = create_hybrid_debug_application()
            
            print("GUI launched successfully!")
            print("- Use 'Create Hybrid Reference' to simulate reference creation")
            print("- Use 'Load Reference' to load existing reference files")
            print("- Use 'Perform Re-alignment' to simulate re-alignment")
            print("- Close the window to continue with the demo")
            
            # Run GUI (will block until window is closed)
            app.exec()
        else:
            print("QApplication already running - skipping GUI demo")
            
    except ImportError as e:
        print(f"GUI demo requires PyQt6: {str(e)}")
    except Exception as e:
        print(f"GUI demo failed: {str(e)}")


def main():
    """Main demo function."""
    print("Hybrid Corner Feature Hashing System Demo")
    print("=" * 50)
    
    # Demo 1: Feature Detection
    features, corner_image = demo_feature_detection()
    
    # Demo 2: Geometric Hashing
    if len(features) >= 3:
        hash_table, matches = demo_geometric_hashing(features)
    else:
        print("Insufficient features for geometric hashing demo")
        hash_table, matches = {}, []
    
    # Demo 3: Reference Creation
    reference_data, ref_file = demo_reference_creation()
    
    # Demo 4: Re-alignment
    realignment_result = demo_realignment(reference_data)
    
    # Demo 5: GUI (optional)
    print("\nWould you like to see the GUI demo? (y/n): ", end="")
    try:
        response = input().strip().lower()
        if response in ['y', 'yes']:
            demo_gui()
    except (EOFError, KeyboardInterrupt):
        print("Skipping GUI demo")
    
    # Summary
    print("\n" + "=" * 50)
    print("Demo Summary:")
    print(f"✓ Feature Detection: {len(features)} features detected")
    print(f"✓ Geometric Hashing: {len(hash_table)} hash buckets generated")
    print(f"✓ Feature Matching: {len(matches)} matches found")
    print(f"✓ Reference Creation: {'Success' if reference_data else 'Failed'}")
    print(f"✓ Re-alignment: {'Success' if realignment_result else 'Failed'}")
    
    # Cleanup
    if ref_file and os.path.exists(ref_file):
        try:
            os.remove(ref_file)
            print(f"Cleaned up demo file: {ref_file}")
        except:
            pass
    
    print("\nDemo completed successfully!")


if __name__ == "__main__":
    main()
