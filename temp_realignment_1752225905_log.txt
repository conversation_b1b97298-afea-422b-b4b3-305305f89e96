================================================================================
SCANNING OPERATION LOG - 2025-07-11 17:25:05
================================================================================
Mode: adaptive
Output CSV: temp_realignment_1752225905.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:/A.Members/张恩浩/python/transfer/20250711
Debug Screenshots: Z:/A.Members/张恩浩/python/transfer/20250711\debug_screenshots
================================================================================

[2025-07-11 17:25:05.053] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-11 17:25:05.071] [INFO] [SYSTEM] Using custom scan folder: 20250711
[2025-07-11 17:25:05.082] [INFO] [SUCCESS] ✓ Camera streaming already active
[2025-07-11 17:25:05.155] [INFO] [STATUS] === HYBRID CORNER RE-ALIGNMENT MODE ===
[2025-07-11 17:25:05.165] [INFO] [STATUS] === STARTING COMPLETE QUICK RE-ALIGNMENT WORKFLOW ===
[2025-07-11 17:25:05.179] [INFO] [STATUS] Using scan folder: Z:/A.Members/张恩浩/python/transfer/20250711
[2025-07-11 17:25:05.202] [INFO] [STATUS] Loading original scan data...
[2025-07-11 17:25:05.225] [INFO] [STATUS] Found original CSV: Z:/A.Members/张恩浩/python/transfer/20250711\20250711_flake_data.csv
[2025-07-11 17:25:05.236] [INFO] [STATUS] Found reference JSON: Z:/A.Members/张恩浩/python/transfer/20250711\20250711_hybrid_reference.json
[2025-07-11 17:25:05.245] [INFO] [STATUS] Loading original flake database...
[2025-07-11 17:25:05.278] [INFO] [STATUS] Loaded 108 original flakes
[2025-07-11 17:25:05.430] [INFO] [STATUS] Loaded reference with 154 features
[2025-07-11 17:25:05.456] [INFO] [WORKFLOW] Finding current upper-left corner position...
[2025-07-11 17:25:05.469] [INFO] [CALIBRATION] Setting zero reference point for re-alignment...
[2025-07-11 17:25:05.616] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-11 17:25:05.626] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-11 17:25:06.478] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-07-11 17:25:08.186] [INFO] [POSITION] Position feedback: (4.23, 0.00) μm
[2025-07-11 17:25:10.405] [INFO] [POSITION] Position feedback: (176.92, 0.00) μm
[2025-07-11 17:25:12.660] [INFO] [POSITION] Position feedback: (355.32, 0.00) μm
[2025-07-11 17:25:14.999] [INFO] [POSITION] Position feedback: (522.51, 0.00) μm
[2025-07-11 17:25:17.256] [INFO] [POSITION] Position feedback: (695.40, 0.00) μm
[2025-07-11 17:25:19.559] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-07-11 17:25:19.705] [INFO] [POSITION] Position feedback: (863.86, 0.00) μm
[2025-07-11 17:25:19.916] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-07-11 17:25:21.855] [INFO] [POSITION] Position feedback: (863.86, 25.61) μm
[2025-07-11 17:25:24.227] [INFO] [POSITION] Position feedback: (863.86, 139.89) μm
[2025-07-11 17:25:26.625] [INFO] [POSITION] Position feedback: (863.86, 262.42) μm
[2025-07-11 17:25:29.035] [INFO] [POSITION] Position feedback: (863.86, 371.62) μm
[2025-07-11 17:25:31.251] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-07-11 17:25:31.385] [INFO] [POSITION] Position feedback: (863.86, 490.34) μm
[2025-07-11 17:25:31.903] [INFO] [WORKFLOW] Starting position (rotation-robust): (490.4, 864.0) μm
[2025-07-11 17:25:31.914] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-07-11 17:25:32.025] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-11 17:25:32.035] [INFO] [STATUS] Skipping reference creation (re-alignment mode)
[2025-07-11 17:25:32.046] [INFO] [SUCCESS] ✓ Corner found for re-alignment - preserving original reference
[2025-07-11 17:25:32.058] [INFO] [STATUS] Current upper-left corner found at: (0.00, 0.00) μm
[2025-07-11 17:25:32.088] [INFO] [STATUS] Capturing current corner screenshot...
[2025-07-11 17:25:32.552] [INFO] [STATUS] Current corner image saved: Z:/A.Members/张恩浩/python/transfer/20250711\20250711_realigned_1752225932.png
[2025-07-11 17:25:32.563] [INFO] [STATUS] Performing feature detection and matching...
[2025-07-11 17:25:32.573] [INFO] [WORKFLOW] [17:25:32.573] Starting hybrid corner re-alignment...
[2025-07-11 17:25:32.583] [INFO] [STATUS] [17:25:32.583] Moving to reference corner: (0.00, 0.00) μm
[2025-07-11 17:25:32.735] [INFO] [POSITION] Position feedback: (0.00, 0.00) μm
[2025-07-11 17:25:33.757] [INFO] [STATUS] [17:25:33.757] Found current corner: (0.00, 0.00) μm
[2025-07-11 17:25:34.204] [INFO] [STATUS] [17:25:34.204] Detecting current corner features...
[2025-07-11 17:25:34.374] [INFO] [STATUS] [17:25:34.374] Loading and deserializing reference hash table...
[2025-07-11 17:25:37.394] [INFO] [SUCCESS] [17:25:37.394] ✓ Deserialized 1774 hash buckets with 5000 triplets
[2025-07-11 17:25:37.405] [INFO] [WORKFLOW] [17:25:37.405] Finding feature matches using geometric hashing...
[2025-07-11 17:25:50.456] [INFO] [STATUS] [17:25:50.456] Found 9 feature matches
[2025-07-11 17:25:50.468] [INFO] [STATUS] [17:25:50.468] Transformation matrix calculated successfully
[2025-07-11 17:25:50.481] [INFO] [STATUS] Applying transformation to original flake coordinates...
[2025-07-11 17:25:50.504] [INFO] [SUCCESS] ✓ Transformed 108 flake coordinates
[2025-07-11 17:25:50.514] [INFO] [STATUS] === QUICK RE-ALIGNMENT COMPLETED SUCCESSFULLY ===
[2025-07-11 17:25:50.537] [INFO] [SUCCESS] ✓ 108 flakes ready for navigation
[2025-07-11 17:25:50.547] [INFO] [STATUS] ERROR: Exception in transformation application: 'confidence'
[2025-07-11 17:25:50.589] [ERROR] [ERROR] Traceback: Traceback (most recent call last):
  File "z:\A.Members\张恩浩\python\transfer\scanning.py", line 1785, in _apply_transformation_and_finalize
    self._emit_and_log_status(f"✓ Transformation confidence: {realignment_result['confidence']:.3f}")
                                                              ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'confidence'

[2025-07-11 17:25:50.602] [INFO] [STATUS] ✗ Hybrid corner re-alignment failed: Exception in transformation application: 'confidence'
[2025-07-11 17:25:50.619] [ERROR] [WORKFLOW] HYBRID-REALIGNMENT workflow completed - FAILURE - Exception in transformation application: 'confidence'
