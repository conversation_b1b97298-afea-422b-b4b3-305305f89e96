================================================================================
SCANNING OPERATION LOG - 2025-07-10 18:25:53
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752143150\scan_1752143150_flake_data.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752143150
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752143150\debug_screenshots
================================================================================

[2025-07-10 18:25:53.849] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-10 18:25:53.862] [INFO] [SYSTEM] Using custom scan folder: scan_1752143150
[2025-07-10 18:25:53.880] [INFO] [SUCCESS] ✓ Camera streaming already active
[2025-07-10 18:25:53.917] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-10 18:25:54.061] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-10 18:25:54.073] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-10 18:25:54.971] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-07-10 18:25:56.720] [INFO] [POSITION] Position feedback: (4.23, 0.00) μm
[2025-07-10 18:25:58.959] [INFO] [POSITION] Position feedback: (176.92, 0.00) μm
[2025-07-10 18:26:01.220] [INFO] [POSITION] Position feedback: (355.32, 0.00) μm
[2025-07-10 18:26:03.500] [INFO] [POSITION] Position feedback: (535.63, 0.00) μm
[2025-07-10 18:26:05.750] [INFO] [POSITION] Position feedback: (718.68, 0.00) μm
[2025-07-10 18:26:08.041] [INFO] [POSITION] Position feedback: (873.38, 0.00) μm
[2025-07-10 18:26:10.570] [INFO] [POSITION] Position feedback: (1040.99, 0.00) μm
[2025-07-10 18:26:13.019] [INFO] [POSITION] Position feedback: (1226.59, 0.00) μm
[2025-07-10 18:26:15.459] [INFO] [POSITION] Position feedback: (1386.58, 0.00) μm
[2025-07-10 18:26:17.929] [INFO] [POSITION] Position feedback: (1572.38, 0.00) μm
[2025-07-10 18:26:20.439] [INFO] [POSITION] Position feedback: (1745.28, 0.00) μm
[2025-07-10 18:26:22.867] [INFO] [POSITION] Position feedback: (1904.85, 0.00) μm
[2025-07-10 18:26:25.190] [INFO] [POSITION] Position feedback: (2077.75, 0.00) μm
[2025-07-10 18:26:27.469] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-07-10 18:26:27.620] [INFO] [POSITION] Position feedback: (2246.20, 0.00) μm
[2025-07-10 18:26:27.841] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-07-10 18:26:29.578] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-07-10 18:26:29.739] [INFO] [POSITION] Position feedback: (2246.20, 0.00) μm
[2025-07-10 18:26:30.253] [INFO] [WORKFLOW] Starting position (rotation-robust): (0.0, 2246.3) μm
[2025-07-10 18:26:30.262] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-07-10 18:26:30.388] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-10 18:26:30.398] [INFO] [STATUS] Creating automatic hybrid corner reference...
[2025-07-10 18:26:30.408] [INFO] [STATUS] Creating hybrid corner reference from current position...
[2025-07-10 18:26:30.836] [INFO] [SUCCESS] ✓ Corner reference image saved: Z:\A.Members\张恩浩\python\transfer\scan_1752143150\scan_1752143150_corner_image.png
[2025-07-10 18:26:30.847] [INFO] [STATUS] Detecting flakes in corner region...
[2025-07-10 18:26:36.342] [INFO] [STATUS] Found 0 flakes in corner region
[2025-07-10 18:26:36.361] [INFO] [STATUS] [18:26:36.360] Creating hybrid corner reference...
[2025-07-10 18:26:36.448] [INFO] [STATUS] [18:26:36.448] Current stage position: (0.00, 0.00) μm
[2025-07-10 18:26:36.458] [INFO] [STATUS] [18:26:36.458] Detecting hybrid features (flakes + edge keypoints)...
[2025-07-10 18:26:37.098] [INFO] [STATUS] [18:26:37.097] Detected 113 total features:
[2025-07-10 18:26:37.110] [INFO] [STATUS] [18:26:37.110]   - 0 flakes
[2025-07-10 18:26:37.120] [INFO] [STATUS] [18:26:37.120]   - 113 edge keypoints
[2025-07-10 18:26:37.129] [INFO] [STATUS] [18:26:37.129] Generating geometric hash table...
[2025-07-10 18:26:37.319] [INFO] [STATUS] [18:26:37.319] Hash table generation completed in 0.18 seconds
[2025-07-10 18:26:37.331] [INFO] [STATUS] [18:26:37.331] Generated 1886 hash buckets
[2025-07-10 18:26:37.341] [INFO] [STATUS] [18:26:37.341] Total triplets stored: 5000
[2025-07-10 18:26:37.361] [INFO] [STATUS] [18:26:37.361] Hybrid corner reference created successfully with 113 features
[2025-07-10 18:26:37.378] [INFO] [SUCCESS] ✓ Automatic hybrid corner reference created successfully
[2025-07-10 18:26:37.391] [INFO] [STATUS] DEBUG: About to save reference to: Z:\A.Members\张恩浩\python\transfer\scan_1752143150\scan_1752143150_hybrid_reference.json
[2025-07-10 18:26:37.411] [INFO] [STATUS] DEBUG: Reference data keys: ['format_version', 'alignment_method', 'creation_timestamp', 'corner_origin_abs', 'features', 'hash_table', 'feature_counts', 'detection_methods', 'metadata', 'edge_detection_method', 'creation_mode', 'scan_mode', 'corner_image_file']
[2025-07-10 18:26:38.965] [INFO] [STATUS] DEBUG: save_hybrid_reference returned: True
[2025-07-10 18:26:38.974] [INFO] [SUCCESS] ✓ Reference saved to: Z:\A.Members\张恩浩\python\transfer\scan_1752143150\scan_1752143150_hybrid_reference.json
[2025-07-10 18:26:38.984] [INFO] [SUCCESS] ✓ MANDATORY hybrid corner reference creation completed successfully
[2025-07-10 18:26:39.003] [INFO] [STATUS] DEBUG: About to return True from _create_automatic_hybrid_corner_reference
[2025-07-10 18:26:39.017] [INFO] [WORKFLOW] Starting adaptive scan...
[2025-07-10 18:26:39.028] [INFO] [WORKFLOW] 
=== Starting Row 0 ===
[2025-07-10 18:26:39.038] [INFO] [STATUS] Scanning row 0 rightward...
[2025-07-10 18:26:41.364] [INFO] [STATUS] Right edge detected: 0/5 right-side points on chip
[2025-07-10 18:26:41.378] [INFO] [STATUS] Row 0: Reached right edge at column 0
[2025-07-10 18:26:41.403] [INFO] [STATUS] Row 0 complete: 0 positions scanned
[2025-07-10 18:26:41.414] [INFO] [WORKFLOW] 
=== Starting Row 1 ===
[2025-07-10 18:26:41.568] [INFO] [POSITION] Position feedback: (0.00, -4.23) μm
[2025-07-10 18:26:46.985] [INFO] [STATUS] Reached bottom edge of chip at row 1
[2025-07-10 18:26:47.016] [INFO] [STATUS] 
=== Scan Complete ===
[2025-07-10 18:26:47.026] [INFO] [STATUS] Total positions: 0
[2025-07-10 18:26:47.036] [INFO] [STATUS] Total rows: 1
[2025-07-10 18:26:47.048] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752143150\scan_1752143150_flake_data.csv
[2025-07-10 18:26:47.058] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-10 18:26:47.070] [INFO] [STATUS] Scanning for annotated images...
[2025-07-10 18:26:47.094] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
