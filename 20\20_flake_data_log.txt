================================================================================
SCANNING OPERATION LOG - 2025-07-10 18:29:45
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\20\20_flake_data.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\20
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\20\debug_screenshots
================================================================================

[2025-07-10 18:29:45.032] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-10 18:29:45.049] [INFO] [SYSTEM] Using custom scan folder: 20
[2025-07-10 18:29:45.065] [INFO] [SUCCESS] ✓ Camera streaming already active
[2025-07-10 18:29:45.085] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-10 18:29:45.201] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-10 18:29:45.214] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-10 18:29:46.013] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-07-10 18:29:47.771] [INFO] [POSITION] Position feedback: (27.72, 0.00) μm
[2025-07-10 18:29:50.021] [INFO] [POSITION] Position feedback: (176.92, 0.00) μm
[2025-07-10 18:29:52.135] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-07-10 18:29:52.281] [INFO] [POSITION] Position feedback: (345.59, 0.00) μm
[2025-07-10 18:29:52.495] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-07-10 18:29:54.187] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-07-10 18:29:54.342] [INFO] [POSITION] Position feedback: (345.59, 0.00) μm
[2025-07-10 18:29:54.862] [INFO] [WORKFLOW] Starting position (rotation-robust): (0.0, 345.6) μm
[2025-07-10 18:29:54.873] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-07-10 18:29:54.971] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-10 18:29:54.982] [INFO] [STATUS] Creating automatic hybrid corner reference...
[2025-07-10 18:29:55.002] [INFO] [STATUS] Creating hybrid corner reference from current position...
[2025-07-10 18:29:55.457] [INFO] [SUCCESS] ✓ Corner reference image saved: Z:\A.Members\张恩浩\python\transfer\20\20_corner_image.png
[2025-07-10 18:29:55.468] [INFO] [STATUS] Detecting flakes in corner region...
[2025-07-10 18:29:58.603] [INFO] [STATUS] Found 0 flakes in corner region
[2025-07-10 18:29:58.613] [INFO] [STATUS] [18:29:58.613] Creating hybrid corner reference...
[2025-07-10 18:29:58.690] [INFO] [STATUS] [18:29:58.690] Current stage position: (0.00, 0.00) μm
[2025-07-10 18:29:58.701] [INFO] [STATUS] [18:29:58.701] Detecting hybrid features (flakes + edge keypoints)...
[2025-07-10 18:29:59.158] [INFO] [STATUS] [18:29:59.158] Detected 150 total features:
[2025-07-10 18:29:59.169] [INFO] [STATUS] [18:29:59.169]   - 0 flakes
[2025-07-10 18:29:59.179] [INFO] [STATUS] [18:29:59.179]   - 150 edge keypoints
[2025-07-10 18:29:59.190] [INFO] [STATUS] [18:29:59.190] Generating geometric hash table...
[2025-07-10 18:29:59.371] [INFO] [STATUS] [18:29:59.371] Hash table generation completed in 0.16 seconds
[2025-07-10 18:29:59.392] [INFO] [STATUS] [18:29:59.392] Generated 2631 hash buckets
[2025-07-10 18:29:59.403] [INFO] [STATUS] [18:29:59.402] Total triplets stored: 5000
[2025-07-10 18:29:59.420] [INFO] [STATUS] [18:29:59.420] Hybrid corner reference created successfully with 150 features
[2025-07-10 18:29:59.446] [INFO] [SUCCESS] ✓ Automatic hybrid corner reference created successfully
[2025-07-10 18:29:59.456] [INFO] [STATUS] DEBUG: About to save reference to: Z:\A.Members\张恩浩\python\transfer\20\20_hybrid_reference.json
[2025-07-10 18:29:59.466] [INFO] [STATUS] DEBUG: Reference data keys: ['format_version', 'alignment_method', 'creation_timestamp', 'corner_origin_abs', 'features', 'hash_table', 'feature_counts', 'detection_methods', 'metadata', 'edge_detection_method', 'creation_mode', 'scan_mode', 'corner_image_file']
