================================================================================
SCANNING OPERATION LOG - 2025-07-10 18:30:18
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752143416\scan_1752143416_flake_data.csv
Grid Steps: 0 x 0
Edge Method: general
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752143416
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752143416\debug_screenshots
================================================================================

[2025-07-10 18:30:18.933] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-10 18:30:18.943] [INFO] [SYSTEM] Using custom scan folder: scan_1752143416
[2025-07-10 18:30:18.963] [INFO] [SUCCESS] ✓ Camera streaming already active
[2025-07-10 18:30:18.991] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-10 18:30:19.115] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-10 18:30:19.127] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-10 18:30:19.841] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-07-10 18:30:21.594] [INFO] [POSITION] Position feedback: (17.78, 0.00) μm
[2025-07-10 18:30:23.750] [INFO] [POSITION] Position feedback: (176.92, 0.00) μm
[2025-07-10 18:30:25.960] [INFO] [POSITION] Position feedback: (349.61, 0.00) μm
[2025-07-10 18:30:28.159] [INFO] [POSITION] Position feedback: (522.51, 0.00) μm
[2025-07-10 18:30:30.360] [INFO] [POSITION] Position feedback: (695.40, 0.00) μm
[2025-07-10 18:30:32.700] [INFO] [POSITION] Position feedback: (868.09, 0.00) μm
[2025-07-10 18:30:34.979] [INFO] [POSITION] Position feedback: (1040.99, 0.00) μm
[2025-07-10 18:30:37.174] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-07-10 18:30:37.319] [INFO] [POSITION] Position feedback: (1209.44, 0.00) μm
[2025-07-10 18:30:37.545] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-07-10 18:30:39.380] [INFO] [POSITION] Position feedback: (1209.44, 4.23) μm
[2025-07-10 18:30:41.739] [INFO] [POSITION] Position feedback: (1209.44, 126.55) μm
[2025-07-10 18:30:44.169] [INFO] [POSITION] Position feedback: (1209.44, 249.30) μm
[2025-07-10 18:30:46.559] [INFO] [POSITION] Position feedback: (1209.44, 371.83) μm
[2025-07-10 18:30:49.000] [INFO] [POSITION] Position feedback: (1209.44, 500.07) μm
[2025-07-10 18:30:51.529] [INFO] [POSITION] Position feedback: (1209.44, 638.48) μm
[2025-07-10 18:30:54.135] [INFO] [POSITION] Position feedback: (1209.44, 760.59) μm
[2025-07-10 18:30:56.582] [INFO] [POSITION] Position feedback: (1209.44, 862.17) μm
[2025-07-10 18:30:59.228] [INFO] [POSITION] Position feedback: (1209.44, 990.20) μm
[2025-07-10 18:31:01.608] [INFO] [POSITION] Position feedback: (1209.44, 1112.94) μm
[2025-07-10 18:31:04.031] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-07-10 18:31:04.198] [INFO] [POSITION] Position feedback: (1209.44, 1225.74) μm
[2025-07-10 18:31:04.712] [INFO] [WORKFLOW] Starting position (rotation-robust): (1225.9, 1209.6) μm
[2025-07-10 18:31:04.723] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-07-10 18:31:04.867] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-10 18:31:04.879] [INFO] [STATUS] Creating automatic hybrid corner reference...
[2025-07-10 18:31:04.892] [INFO] [STATUS] Creating hybrid corner reference from current position...
[2025-07-10 18:31:05.326] [INFO] [SUCCESS] ✓ Corner reference image saved: Z:\A.Members\张恩浩\python\transfer\scan_1752143416\scan_1752143416_corner_image.png
[2025-07-10 18:31:05.338] [INFO] [STATUS] Detecting flakes in corner region...
[2025-07-10 18:31:08.769] [INFO] [STATUS] Found 0 flakes in corner region
[2025-07-10 18:31:08.790] [INFO] [STATUS] [18:31:08.790] Creating hybrid corner reference...
[2025-07-10 18:31:08.895] [INFO] [STATUS] [18:31:08.894] Current stage position: (0.00, 0.00) μm
[2025-07-10 18:31:08.905] [INFO] [STATUS] [18:31:08.905] Detecting hybrid features (flakes + edge keypoints)...
[2025-07-10 18:31:08.974] [ERROR] [ERROR] ✗ CRITICAL: Failed to create hybrid corner reference: Insufficient features detected: 0 < 3
[2025-07-10 18:31:08.985] [INFO] [STATUS]   → Feature detection or corner analysis failed
[2025-07-10 18:31:08.995] [INFO] [STATUS]   → Check chip positioning and corner visibility
[2025-07-10 18:31:09.007] [ERROR] [ERROR] ✗ CRITICAL: Failed to create hybrid corner reference. Scanning cannot proceed.
[2025-07-10 18:31:09.017] [ERROR] [ERROR] ⚠ Warning: Failed to set zero reference: CRITICAL: Failed to create hybrid corner reference. Scanning cannot proceed.
[2025-07-10 18:31:09.031] [INFO] [WORKFLOW] Starting adaptive scan...
[2025-07-10 18:31:09.053] [INFO] [WORKFLOW] 
=== Starting Row 0 ===
[2025-07-10 18:31:09.063] [INFO] [STATUS] Scanning row 0 rightward...
[2025-07-10 18:31:11.505] [INFO] [STATUS] Right edge detected: 0/5 right-side points on chip
[2025-07-10 18:31:11.517] [INFO] [STATUS] Row 0: Reached right edge at column 0
[2025-07-10 18:31:11.527] [INFO] [STATUS] Row 0 complete: 0 positions scanned
[2025-07-10 18:31:11.537] [INFO] [WORKFLOW] 
=== Starting Row 1 ===
[2025-07-10 18:31:11.709] [INFO] [POSITION] Position feedback: (4.23, 4.02) μm
[2025-07-10 18:31:17.107] [INFO] [STATUS] Reached bottom edge of chip at row 1
[2025-07-10 18:31:17.123] [INFO] [STATUS] 
=== Scan Complete ===
[2025-07-10 18:31:17.133] [INFO] [STATUS] Total positions: 0
[2025-07-10 18:31:17.157] [INFO] [STATUS] Total rows: 1
[2025-07-10 18:31:17.169] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752143416\scan_1752143416_flake_data.csv
