#!/usr/bin/env python3
"""
Hybrid Corner Feature Hashing System for Chip Re-alignment

This module implements a robust chip re-alignment system that combines flake detection 
with edge keypoint detection in the upper-left corner region, using geometric hashing 
for fast and accurate feature matching.

Key Features:
- Heterogeneous feature detection (flakes + edge keypoints)
- Geometric hashing for rotation/translation invariant matching
- Sub-micrometer alignment accuracy
- Fast re-alignment without full chip rescans
- Comprehensive logging and debugging tools

Author: Augment Agent
Date: 2025-07-02
"""

import cv2
import numpy as np
import json
import time
import logging
import os
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime

# Direct camera API access only - no screenshot fallback

from edge_detection import CannyEdgeDetector, EdgeDetector, BackgroundEdgeDetector
from config import STEP_Y_UM, STEP_X_UM
from camera_manager import CentralizedCameraManager


# Local definitions to avoid circular imports
class Flake:
    """Simple flake data structure for hybrid alignment workflow"""
    def __init__(self, id, center_x, center_y, real_x_um, real_y_um, shape, class_name):
        self.id = id
        self.center_x = center_x
        self.center_y = center_y
        self.real_x_um = real_x_um
        self.real_y_um = real_y_um
        self.shape = shape
        self.class_name = class_name
        self.area = 0  # Default value


class StageController:
    """Mock stage controller interface for hybrid alignment"""
    def __init__(self, status_callback=None):
        self.status_callback = status_callback or (lambda x: None)
        self.position = [0.0, 0.0]

    def get_position(self):
        return self.position

    def move_absolute(self, x, y):
        self.position = [x, y]
        if self.status_callback:
            self.status_callback(f"Stage moved to ({x:.2f}, {y:.2f})")

    def set_zero(self):
        self.position = [0.0, 0.0]
        if self.status_callback:
            self.status_callback("Stage zero position set")

    def close(self):
        pass


@dataclass
class HybridFeature:
    """
    Unified feature representation for both flakes and edge keypoints.
    
    This class provides a common interface for handling different types of features
    detected in the corner region, enabling unified processing in the geometric
    hashing system.
    """
    id: str
    feature_type: str  # "flake" or "edge_keypoint"
    x_um: float  # Absolute stage coordinates in micrometers
    y_um: float  # Absolute stage coordinates in micrometers
    pixel_x: float  # Pixel coordinates in the corner image
    pixel_y: float  # Pixel coordinates in the corner image
    confidence: float  # Detection confidence score (0.0 - 1.0)
    
    # Feature-specific data
    flake_data: Optional[Dict] = None  # For flakes: shape, class, area, etc.
    keypoint_data: Optional[Dict] = None  # For edge keypoints: response, angle, octave, etc.
    
    def __post_init__(self):
        """Validate feature data consistency."""
        if self.feature_type == "flake" and self.flake_data is None:
            raise ValueError("Flake features must have flake_data")
        if self.feature_type == "edge_keypoint" and self.keypoint_data is None:
            raise ValueError("Edge keypoint features must have keypoint_data")


class EdgeKeypointDetector:
    """
    Detects stable keypoints on chip edges using ORB (Oriented FAST and Rotated BRIEF).
    
    This detector finds distinctive features like jags, notches, and curves on the
    chip edges that can be reliably matched across different scanning sessions.
    """
    
    def __init__(self, max_keypoints: int = 500, debug: bool = False):
        """
        Initialize the edge keypoint detector.
        
        Args:
            max_keypoints: Maximum number of keypoints to detect
            debug: Enable debug output and visualization
        """
        self.max_keypoints = max_keypoints
        self.debug = debug
        
        # Initialize ORB detector with optimized parameters for edge detection
        self.orb = cv2.ORB_create(
            nfeatures=max_keypoints,
            scaleFactor=1.2,
            nlevels=8,
            edgeThreshold=31,
            firstLevel=0,
            WTA_K=2,
            scoreType=cv2.ORB_HARRIS_SCORE,
            patchSize=31,
            fastThreshold=20
        )
        
        self.logger = logging.getLogger(__name__)
    
    def detect_edge_keypoints(self, corner_image: np.ndarray, edge_mask: np.ndarray) -> List[HybridFeature]:
        """
        Detect stable keypoints on chip edges.
        
        Args:
            corner_image: Corner region image (BGR format)
            edge_mask: Binary mask where True indicates chip edges
            
        Returns:
            List of HybridFeature objects representing edge keypoints
        """
        try:
            # Convert to grayscale for ORB detection
            if len(corner_image.shape) == 3:
                gray = cv2.cvtColor(corner_image, cv2.COLOR_BGR2GRAY)
            else:
                gray = corner_image.copy()
            
            # Apply edge mask to focus detection on chip edges
            masked_gray = cv2.bitwise_and(gray, edge_mask)
            
            # Detect keypoints and compute descriptors
            keypoints, descriptors = self.orb.detectAndCompute(masked_gray, edge_mask)
            
            if self.debug:
                self.logger.info(f"Detected {len(keypoints)} edge keypoints")
            
            # Convert keypoints to HybridFeature objects
            features = []
            for i, kp in enumerate(keypoints):
                # Filter keypoints to ensure they are on actual edges
                if self._is_on_edge(kp, edge_mask):
                    feature = HybridFeature(
                        id=f"edge_kp_{i}",
                        feature_type="edge_keypoint",
                        x_um=0.0,  # Will be set by caller with stage coordinates
                        y_um=0.0,  # Will be set by caller with stage coordinates
                        pixel_x=kp.pt[0],
                        pixel_y=kp.pt[1],
                        confidence=kp.response / 100.0,  # Normalize response to 0-1
                        keypoint_data={
                            'response': kp.response,
                            'angle': kp.angle,
                            'octave': kp.octave,
                            'class_id': kp.class_id,
                            'descriptor': descriptors[i].tolist() if descriptors is not None else None
                        }
                    )
                    features.append(feature)
            
            if self.debug:
                self.logger.info(f"Filtered to {len(features)} edge keypoints on actual edges")
            
            return features
            
        except Exception as e:
            self.logger.error(f"Edge keypoint detection failed: {str(e)}")
            return []
    
    def _is_on_edge(self, keypoint: cv2.KeyPoint, edge_mask: np.ndarray, 
                    tolerance: int = 3) -> bool:
        """
        Check if a keypoint is actually on a chip edge.
        
        Args:
            keypoint: OpenCV KeyPoint object
            edge_mask: Binary edge mask
            tolerance: Pixel tolerance for edge proximity
            
        Returns:
            True if keypoint is on an edge, False otherwise
        """
        x, y = int(keypoint.pt[0]), int(keypoint.pt[1])
        
        # Check bounds
        if (x < tolerance or y < tolerance or 
            x >= edge_mask.shape[1] - tolerance or 
            y >= edge_mask.shape[0] - tolerance):
            return False
        
        # Check if keypoint is within tolerance of an edge pixel
        roi = edge_mask[y-tolerance:y+tolerance+1, x-tolerance:x+tolerance+1]
        return np.any(roi > 0)


class HybridFeatureDetector:
    """
    Combines flake detection with edge keypoint detection for comprehensive feature extraction.
    
    This class orchestrates the detection of both flakes and edge keypoints in the corner
    region, providing a unified interface for feature extraction.
    """
    
    def __init__(self, edge_detector: Optional[EdgeDetector] = None, debug: bool = False):
        """
        Initialize the hybrid feature detector.
        
        Args:
            edge_detector: Edge detector instance for chip edge detection
            debug: Enable debug output and visualization
        """
        self.edge_detector = edge_detector or CannyEdgeDetector(debug=debug)
        self.edge_keypoint_detector = EdgeKeypointDetector(debug=debug)
        self.debug = debug
        self.logger = logging.getLogger(__name__)
    
    def detect_features(self, corner_image: np.ndarray, flakes: List[Flake], 
                       stage_position: Tuple[float, float]) -> List[HybridFeature]:
        """
        Detect all features (flakes + edge keypoints) in the corner region.
        
        Args:
            corner_image: Corner region image (BGR format)
            flakes: List of detected flakes in the corner region
            stage_position: Current stage position (y_um, x_um)
            
        Returns:
            List of HybridFeature objects representing all detected features
        """
        features = []
        
        try:
            # Step 1: Convert flakes to HybridFeature objects
            flake_features = self._convert_flakes_to_features(flakes)
            features.extend(flake_features)
            
            if self.debug:
                self.logger.info(f"Converted {len(flake_features)} flakes to features")
            
            # Step 2: Detect chip edges for keypoint detection
            edge_result = self.edge_detector.detect_edges_with_line_fitting(
                corner_image, algorithm_mode='sequential'
            )
            
            if edge_result and 'edges' in edge_result:
                # Step 3: Detect edge keypoints
                edge_features = self.edge_keypoint_detector.detect_edge_keypoints(
                    corner_image, edge_result['edges']
                )
                
                # Set stage coordinates for edge keypoints
                for feature in edge_features:
                    # Convert pixel coordinates to stage coordinates
                    # This is a simplified conversion - in practice, you'd use proper calibration
                    pixel_to_um_x = STEP_X_UM / corner_image.shape[0]
                    pixel_to_um_y = STEP_Y_UM / corner_image.shape[1]
                    
                    feature.x_um = stage_position[1] + (feature.pixel_x - corner_image.shape[1]/2) * pixel_to_um_y
                    feature.y_um = stage_position[0] + (feature.pixel_y - corner_image.shape[0]/2) * pixel_to_um_x
                
                features.extend(edge_features)
                
                if self.debug:
                    self.logger.info(f"Added {len(edge_features)} edge keypoint features")
            
            if self.debug:
                self.logger.info(f"Total features detected: {len(features)}")
            
            return features
            
        except Exception as e:
            self.logger.error(f"Hybrid feature detection failed: {str(e)}")
            return features  # Return partial results
    
    def _convert_flakes_to_features(self, flakes: List[Flake]) -> List[HybridFeature]:
        """
        Convert Flake objects to HybridFeature objects.
        
        Args:
            flakes: List of Flake objects
            
        Returns:
            List of HybridFeature objects
        """
        features = []
        
        for flake in flakes:
            feature = HybridFeature(
                id=flake.id,
                feature_type="flake",
                x_um=flake.real_x_um,
                y_um=flake.real_y_um,
                pixel_x=flake.center_x,
                pixel_y=flake.center_y,
                confidence=1.0,  # Flakes have high confidence by default
                flake_data={
                    'shape': flake.shape,
                    'class_name': flake.class_name,
                    'area': getattr(flake, 'area', 0)
                }
            )
            features.append(feature)
        
        return features


class GeometricHasher:
    """
    Implements geometric hashing for rotation and translation invariant feature matching.

    This class generates hash keys based on triangle geometry formed by feature triplets,
    enabling fast and robust feature matching even under rotation and translation.
    """

    def __init__(self, hash_precision: int = 3, debug: bool = False, match_threshold: float = 0.7,
                 max_features: int = 100, max_triplets: int = 10000, spatial_threshold: float = 500.0):
        """
        Initialize the geometric hasher with performance optimization parameters.

        Args:
            hash_precision: Number of decimal places for hash key precision
            debug: Enable debug output
            match_threshold: Minimum match quality threshold (0.0 - 1.0)
            max_features: Maximum number of features to use for hashing (performance limit)
            max_triplets: Maximum number of triplets to generate (performance limit)
            spatial_threshold: Maximum distance between features in a triplet (micrometers)
        """
        self.hash_precision = hash_precision
        self.debug = debug
        self.match_threshold = match_threshold
        self.max_features = max_features
        self.max_triplets = max_triplets
        self.spatial_threshold = spatial_threshold
        self.logger = logging.getLogger(__name__)

        # Hash table: hash_key -> list of feature triplets
        self.hash_table: Dict[str, List[Dict]] = {}

    def _select_best_features(self, features: List[HybridFeature]) -> List[HybridFeature]:
        """
        Select the best features for geometric hashing to limit computational complexity.

        Args:
            features: List of all detected features

        Returns:
            List of selected features (limited to max_features)
        """
        if len(features) <= self.max_features:
            return features

        # Sort features by quality (confidence score)
        sorted_features = sorted(features, key=lambda f: f.confidence, reverse=True)

        # Take top features by confidence
        selected_features = sorted_features[:self.max_features]

        if self.debug:
            self.logger.info(f"Selected {len(selected_features)} best features from {len(features)} total")

        return selected_features

    def _is_valid_triplet(self, triplet: List[HybridFeature]) -> bool:
        """
        Check if a triplet is valid for geometric hashing based on spatial constraints.

        Args:
            triplet: List of three features

        Returns:
            True if triplet meets spatial constraints
        """
        # Check maximum distance constraint
        for i in range(3):
            for j in range(i + 1, 3):
                f1, f2 = triplet[i], triplet[j]
                distance = ((f1.x_um - f2.x_um) ** 2 + (f1.y_um - f2.y_um) ** 2) ** 0.5
                if distance > self.spatial_threshold:
                    return False

        # Check minimum triangle area (avoid degenerate triangles)
        f1, f2, f3 = triplet
        area = abs((f1.x_um * (f2.y_um - f3.y_um) +
                   f2.x_um * (f3.y_um - f1.y_um) +
                   f3.x_um * (f1.y_um - f2.y_um)) / 2.0)

        # Minimum area threshold (avoid nearly collinear points)
        min_area = 100.0  # square micrometers
        return area > min_area

    def generate_hash_table(self, features: List[HybridFeature]) -> Dict[str, List[Dict]]:
        """
        Generate optimized geometric hash table from a list of features.

        Uses intelligent feature selection and spatial constraints to limit
        computational complexity and prevent exponential growth.

        Args:
            features: List of HybridFeature objects

        Returns:
            Hash table mapping hash keys to feature triplets
        """
        self.hash_table.clear()

        if len(features) < 3:
            if self.debug:
                self.logger.warning(f"Insufficient features for hashing: {len(features)} < 3")
            return self.hash_table

        # Step 1: Select best features to limit complexity
        selected_features = self._select_best_features(features)

        if self.debug:
            total_possible_triplets = len(selected_features) * (len(selected_features) - 1) * (len(selected_features) - 2) // 6
            self.logger.info(f"Processing {len(selected_features)} features (max possible triplets: {total_possible_triplets})")

        # Step 2: Generate triplets with spatial and quality constraints
        triplet_count = 0
        valid_triplet_count = 0

        for i in range(len(selected_features)):
            for j in range(i + 1, len(selected_features)):
                for k in range(j + 1, len(selected_features)):
                    triplet = [selected_features[i], selected_features[j], selected_features[k]]
                    triplet_count += 1

                    # Check spatial constraints
                    if not self._is_valid_triplet(triplet):
                        continue

                    # Check triplet limit
                    if valid_triplet_count >= self.max_triplets:
                        if self.debug:
                            self.logger.warning(f"Reached maximum triplet limit: {self.max_triplets}")
                        break

                    hash_key = self._compute_triangle_hash(triplet)

                    if hash_key is not None:
                        if hash_key not in self.hash_table:
                            self.hash_table[hash_key] = []

                        self.hash_table[hash_key].append({
                            'features': triplet,
                            'feature_ids': [f.id for f in triplet],
                            'feature_types': [f.feature_type for f in triplet],
                            'coordinates': [(f.x_um, f.y_um) for f in triplet],
                            'confidences': [f.confidence for f in triplet]
                        })
                        valid_triplet_count += 1

                # Break outer loops if limit reached
                if valid_triplet_count >= self.max_triplets:
                    break
            if valid_triplet_count >= self.max_triplets:
                break

        if self.debug:
            self.logger.info(f"Generated {valid_triplet_count} valid triplets from {triplet_count} candidates in {len(self.hash_table)} hash buckets")

        return self.hash_table

    def find_matches(self, query_features: List[HybridFeature],
                    min_matches: int = 3) -> List[Dict]:
        """
        Find matching feature triplets using geometric hashing.

        Args:
            query_features: List of features to match against hash table
            min_matches: Minimum number of matching triplets required

        Returns:
            List of matching triplet pairs with transformation information
        """
        matches = []

        if len(query_features) < 3:
            if self.debug:
                self.logger.warning(f"Insufficient query features: {len(query_features)} < 3")
            return matches

        # Generate triplets from query features and look for matches
        for i in range(len(query_features)):
            for j in range(i + 1, len(query_features)):
                for k in range(j + 1, len(query_features)):
                    query_triplet = [query_features[i], query_features[j], query_features[k]]
                    hash_key = self._compute_triangle_hash(query_triplet)

                    if hash_key is not None and hash_key in self.hash_table:
                        # Found potential matches
                        for reference_triplet_data in self.hash_table[hash_key]:
                            # Verify match quality
                            match_quality = self._verify_triplet_match(
                                query_triplet, reference_triplet_data['features']
                            )

                            if match_quality > self.match_threshold:  # Configurable threshold for good matches
                                matches.append({
                                    'query_triplet': query_triplet,
                                    'reference_triplet': reference_triplet_data,
                                    'match_quality': match_quality,
                                    'hash_key': hash_key
                                })

        # Sort matches by quality
        matches.sort(key=lambda x: x['match_quality'], reverse=True)

        if self.debug:
            self.logger.info(f"Found {len(matches)} potential triplet matches")

        return matches[:min_matches * 3]  # Return top matches

    def _compute_triangle_hash(self, triplet: List[HybridFeature]) -> Optional[str]:
        """
        Compute rotation and translation invariant hash for a feature triplet.

        Args:
            triplet: List of three HybridFeature objects

        Returns:
            Hash key string or None if triangle is degenerate
        """
        if len(triplet) != 3:
            return None

        # Extract coordinates
        points = np.array([(f.x_um, f.y_um) for f in triplet])

        # Calculate side lengths
        side1 = np.linalg.norm(points[1] - points[0])
        side2 = np.linalg.norm(points[2] - points[1])
        side3 = np.linalg.norm(points[0] - points[2])

        # Check for degenerate triangle
        if min(side1, side2, side3) < 1e-6:  # Very small triangle
            return None

        # Sort side lengths to make hash rotation invariant
        sides = sorted([side1, side2, side3])

        # Create hash key from sorted side lengths
        hash_key = f"{sides[0]:.{self.hash_precision}f}_{sides[1]:.{self.hash_precision}f}_{sides[2]:.{self.hash_precision}f}"

        return hash_key

    def _verify_triplet_match(self, query_triplet: List[HybridFeature],
                             reference_triplet: List[HybridFeature]) -> float:
        """
        Verify the quality of a triplet match.

        Args:
            query_triplet: Query feature triplet
            reference_triplet: Reference feature triplet

        Returns:
            Match quality score (0.0 - 1.0)
        """
        # Check feature type compatibility
        query_types = [f.feature_type for f in query_triplet]
        ref_types = [f.feature_type for f in reference_triplet]

        # Count matching feature types
        type_matches = 0
        for qt in query_types:
            if qt in ref_types:
                type_matches += 1

        type_score = type_matches / 3.0

        # Check confidence scores
        query_conf = np.mean([f.confidence for f in query_triplet])
        ref_conf = np.mean([f.confidence for f in reference_triplet])
        conf_score = min(query_conf, ref_conf)

        # Combine scores
        overall_score = 0.6 * type_score + 0.4 * conf_score

        return overall_score


class HybridCornerAlignmentSystem:
    """
    Main system for hybrid corner feature-based chip re-alignment.

    This class orchestrates the entire workflow from initial corner indexing
    and reference creation to fast re-alignment using geometric hashing.
    """

    def __init__(self, stage_controller: StageController, region: Tuple[int, int, int, int],
                 status_callback: Optional[callable] = None, debug: bool = False):
        """
        Initialize the hybrid corner alignment system.

        Args:
            stage_controller: Stage controller instance
            region: Screen capture region (x, y, width, height)
            status_callback: Optional callback for status messages
            debug: Enable debug output and visualization
        """
        self.stage = stage_controller
        self.region = region
        self.status_callback = status_callback or (lambda x: None)
        self.debug = debug

        # Initialize camera manager for direct camera access
        self.camera_manager = self._create_camera_manager()

        # Initialize components
        self.feature_detector = HybridFeatureDetector(debug=debug)
        self.geometric_hasher = GeometricHasher(
            hash_precision=0,
            debug=debug,
            match_threshold=0.5,
            max_features=100,      # Limit to top 100 features by confidence
            max_triplets=5000,     # Limit to 5000 triplets maximum
            spatial_threshold=300.0  # 300 micrometer maximum distance between features
        )

        # Setup logging
        self.logger = logging.getLogger(__name__)
        if debug:
            self.logger.setLevel(logging.DEBUG)

        # Debug output folder
        self.debug_screenshots_folder = None

    def _create_camera_manager(self):
        """Create centralized camera manager for coordinated camera access."""
        try:
            # Use the CentralizedCameraManager for coordinated camera access
            return CentralizedCameraManager(camera_index=0)
        except Exception as e:
            self._log_status(f"⚠ Camera manager creation failed: {e}")
            return None

    def create_hybrid_reference(self, corner_flakes: List[Flake],
                               corner_image: np.ndarray,
                               metadata: Optional[Dict] = None) -> Dict:
        """
        Create hybrid corner reference with both flakes and edge keypoints.

        This method implements Phase 1 of the hybrid alignment system:
        1. Detect features (flakes + edge keypoints) in corner region
        2. Generate geometric hash table
        3. Save reference data to file

        Args:
            corner_flakes: List of flakes detected in the upper-left corner
            corner_image: Corner region image
            metadata: Optional metadata for the reference

        Returns:
            Dict with success status and reference data
        """
        try:
            self._log_status("Creating hybrid corner reference...")

            # Step 1: Get current stage position
            stage_pos = self.stage.get_position()
            self._log_status(f"Current stage position: ({stage_pos[0]:.2f}, {stage_pos[1]:.2f}) μm")

            # Step 2: Detect all features in corner region
            self._log_status("Detecting hybrid features (flakes + edge keypoints)...")
            features = self.feature_detector.detect_features(
                corner_image, corner_flakes, stage_pos
            )

            if len(features) < 3:
                return {
                    'success': False,
                    'error': f'Insufficient features detected: {len(features)} < 3'
                }

            self._log_status(f"Detected {len(features)} total features:")
            flake_count = sum(1 for f in features if f.feature_type == "flake")
            keypoint_count = sum(1 for f in features if f.feature_type == "edge_keypoint")
            self._log_status(f"  - {flake_count} flakes")
            self._log_status(f"  - {keypoint_count} edge keypoints")

            # Step 3: Generate geometric hash table with performance monitoring
            self._log_status("Generating geometric hash table...")
            hash_start_time = time.time()
            hash_table = self.geometric_hasher.generate_hash_table(features)
            hash_duration = time.time() - hash_start_time

            self._log_status(f"Hash table generation completed in {hash_duration:.2f} seconds")
            self._log_status(f"Generated {len(hash_table)} hash buckets")

            # Calculate total triplets for monitoring
            total_triplets = sum(len(bucket) for bucket in hash_table.values())
            self._log_status(f"Total triplets stored: {total_triplets}")

            if not hash_table:
                return {
                    'success': False,
                    'error': 'Failed to generate geometric hash table'
                }

            # Step 4: Create reference data structure
            reference_data = {
                'format_version': '3.0',  # New version for hybrid alignment
                'alignment_method': 'hybrid_corner',
                'creation_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'corner_origin_abs': [stage_pos[0], stage_pos[1]],
                'features': [asdict(f) for f in features],
                'hash_table': self._serialize_hash_table(hash_table),  # Serialize hash table with feature conversion
                'feature_counts': {
                    'total': len(features),
                    'flakes': flake_count,
                    'edge_keypoints': keypoint_count
                },
                'detection_methods': {
                    'edge_detector': self.feature_detector.edge_detector.__class__.__name__,
                    'orb_parameters': {
                        'max_keypoints': self.feature_detector.edge_keypoint_detector.max_keypoints,
                        'scale_factor': 1.2,
                        'n_levels': 8
                    }
                },
                'metadata': metadata or {}
            }

            # Step 5: Save debug image if enabled
            if self.debug and self.debug_screenshots_folder:
                self._save_debug_image(corner_image, features, "reference_creation")

            self._log_status(f"Hybrid corner reference created successfully with {len(features)} features")

            return {
                'success': True,
                'reference_data': reference_data,
                'features': features,
                'hash_table': hash_table
            }

        except Exception as e:
            error_msg = f"Hybrid reference creation failed: {str(e)}"
            self._log_status(error_msg)
            self.logger.error(error_msg, exc_info=True)
            return {'success': False, 'error': error_msg}

    def perform_hybrid_realignment(self, reference_data: Dict,
                                  current_flakes: List[Flake] = None) -> Dict:
        """
        Perform fast re-alignment using hybrid corner features.

        This method implements Phase 2 of the hybrid alignment system:
        1. Re-scan corner region and detect current features
        2. Match features using geometric hashing
        3. Calculate transformation matrix
        4. Apply transformation to original scan coordinates

        Args:
            reference_data: Reference data from create_hybrid_reference()
            current_flakes: Optional current flakes (if None, will detect from corner)

        Returns:
            Dict with success status and transformation results
        """
        try:
            self._log_status("Starting hybrid corner re-alignment...")

            # Step 1: Move to reference corner position
            ref_corner = reference_data['corner_origin_abs']
            self._log_status(f"Moving to reference corner: ({ref_corner[0]:.2f}, {ref_corner[1]:.2f}) μm")
            self.stage.move_absolute(ref_corner[0], ref_corner[1])
            time.sleep(0.5)  # Allow stage to settle

            # Step 2: Find current upper-left corner
            current_corner = self._find_current_corner()
            if current_corner is None:
                return {'success': False, 'error': 'Failed to find current corner'}

            self._log_status(f"Found current corner: ({current_corner[0]:.2f}, {current_corner[1]:.2f}) μm")

            # Step 3: Capture current corner image using camera manager
            if self.camera_manager:
                corner_image = self.camera_manager.get_streaming_image()
            else:
                raise RuntimeError("Camera manager not available for corner image capture")

            # Step 4: Detect current features
            self._log_status("Detecting current corner features...")
            if current_flakes is None:
                current_flakes = []  # In practice, would detect flakes from corner image

            current_features = self.feature_detector.detect_features(
                corner_image, current_flakes, current_corner
            )

            if len(current_features) < 3:
                return {
                    'success': False,
                    'error': f'Insufficient current features: {len(current_features)} < 3'
                }

            # Step 5: Load reference hash table and deserialize features
            self._log_status("Loading and deserializing reference hash table...")
            raw_hash_table = reference_data['hash_table']

            # Deserialize features in hash table from dictionaries to HybridFeature objects
            deserialized_hash_table = {}
            total_triplets = 0

            try:
                for hash_key, triplet_list in raw_hash_table.items():
                    deserialized_triplets = []
                    for triplet_data in triplet_list:
                        # Convert feature data back to HybridFeature objects
                        features = []
                        raw_features = triplet_data['features']

                        for feature_data in raw_features:
                            if isinstance(feature_data, str):
                                # Parse string representation of HybridFeature back to object
                                feature = self._parse_hybrid_feature_string(feature_data)
                            elif isinstance(feature_data, dict):
                                # Convert dictionary to HybridFeature object
                                feature = HybridFeature(**feature_data)
                            else:
                                # Already a HybridFeature object
                                feature = feature_data
                            features.append(feature)

                        # Update the triplet data with deserialized features
                        deserialized_triplet = triplet_data.copy()
                        deserialized_triplet['features'] = features
                        deserialized_triplets.append(deserialized_triplet)
                        total_triplets += 1

                    deserialized_hash_table[hash_key] = deserialized_triplets

                self.geometric_hasher.hash_table = deserialized_hash_table
                self._log_status(f"✓ Deserialized {len(deserialized_hash_table)} hash buckets with {total_triplets} triplets")

            except Exception as e:
                self._log_status(f"✗ Error deserializing hash table: {str(e)}")
                raise RuntimeError(f"Failed to deserialize reference hash table: {str(e)}")

            # Step 6: Find feature matches
            self._log_status("Finding feature matches using geometric hashing...")
            matches = self.geometric_hasher.find_matches(current_features, min_matches=3)

            if len(matches) < 3:
                return {
                    'success': False,
                    'error': f'Insufficient feature matches: {len(matches)} < 3'
                }

            self._log_status(f"Found {len(matches)} feature matches")

            # Step 7: Calculate transformation matrix
            transformation_result = self._calculate_transformation_matrix(matches)
            if not transformation_result['success']:
                return transformation_result

            # Step 8: Apply transformation to original coordinates
            # This would transform all flake coordinates from the original scan
            self._log_status("Transformation matrix calculated successfully")

            # Step 9: Save debug visualization if enabled
            if self.debug and self.debug_screenshots_folder:
                self._save_debug_image(corner_image, current_features, "realignment")
                self._save_match_visualization(matches, "feature_matches")

            # Convert the raw affine matrix to the expected format
            matrix = np.array(transformation_result['matrix'])
            confidences = np.array([c['confidence'] for c in transformation_result['correspondences']])

            # Extract rotation matrix (2x2) and translation vector (2x1)
            rotation_matrix = matrix[:2, :2]
            translation = matrix[:2, 2]

            return {
                'success': True,
                'transformation_matrix': {
                    'rotation_matrix': rotation_matrix.tolist(),
                    'translation': translation.tolist(),
                    'confidence': confidences.tolist(),
                    'raw_matrix': transformation_result['matrix']  # Keep original for reference
                },
                'corner_offset': (
                    current_corner[0] - ref_corner[0],
                    current_corner[1] - ref_corner[1]
                ),
                'feature_matches': len(matches),
                'match_quality': np.mean([m['match_quality'] for m in matches])
            }

        except Exception as e:
            error_msg = f"Hybrid re-alignment failed: {str(e)}"
            self._log_status(error_msg)
            self.logger.error(error_msg, exc_info=True)
            return {'success': False, 'error': error_msg}

    def _find_current_corner(self) -> Optional[Tuple[float, float]]:
        """
        Find the current upper-left corner using edge detection.

        Returns:
            Tuple of (y_um, x_um) coordinates or None if failed
        """
        try:
            # Use the same corner finding logic as the existing scanning system
            # This is a simplified version - in practice, would use the full edge detection workflow

            # Capture image for edge detection using camera manager
            if self.camera_manager:
                img = self.camera_manager.get_streaming_image()
            else:
                raise RuntimeError("Camera manager not available for edge detection")

            # Use edge detector to find corner
            edge_result = self.feature_detector.edge_detector.detect_edges_with_line_fitting(
                img, algorithm_mode='sequential'
            )

            if edge_result and 'fitted_lines' in edge_result:
                # Calculate corner from fitted lines (simplified)
                # In practice, would use the full corner calculation logic
                current_pos = self.stage.get_position()
                return current_pos  # Simplified - return current position

            return None

        except Exception as e:
            self.logger.error(f"Corner finding failed: {str(e)}")
            return None

    def _calculate_transformation_matrix(self, matches: List[Dict]) -> Dict:
        """
        Calculate affine transformation matrix from feature matches.

        Uses individual feature correspondences instead of triangle centroids
        to ensure accurate translation calculation.

        Args:
            matches: List of feature match dictionaries

        Returns:
            Dict with success status and transformation matrix
        """
        try:
            if len(matches) < 3:
                return {'success': False, 'error': 'Need at least 3 matches for transformation'}

            # Extract individual feature correspondences from triangle matches
            src_points = []
            dst_points = []
            correspondence_info = []

            for match_idx, match in enumerate(matches):
                ref_triplet = match['reference_triplet']['features']
                query_triplet = match['query_triplet']

                # Find best correspondences between individual features in the triangles
                correspondences = self._find_feature_correspondences(ref_triplet, query_triplet)

                for ref_feat, query_feat, confidence in correspondences:
                    src_points.append((query_feat.x_um, query_feat.y_um))
                    dst_points.append((ref_feat.x_um, ref_feat.y_um))
                    correspondence_info.append({
                        'match_idx': match_idx,
                        'confidence': confidence,
                        'ref_feature_id': ref_feat.id,
                        'query_feature_id': query_feat.id
                    })

            if len(src_points) < 3:
                return {'success': False, 'error': 'Need at least 3 point correspondences for transformation'}

            # Convert to numpy arrays
            src_points = np.array(src_points, dtype=np.float32)
            dst_points = np.array(dst_points, dtype=np.float32)

            # Use robust transformation estimation if we have many points
            if len(src_points) > 3:
                # Use RANSAC for robust estimation with many correspondences
                matrix, inliers = cv2.estimateAffinePartial2D(
                    src_points, dst_points,
                    method=cv2.RANSAC,
                    ransacReprojThreshold=2.0,  # 2 micrometer threshold
                    maxIters=1000,
                    confidence=0.99
                )

                if matrix is None:
                    # Fallback to least squares with top 3 points
                    matrix = cv2.getAffineTransform(src_points[:3], dst_points[:3])
                    inliers = np.ones((len(src_points), 1), dtype=np.uint8)

                inlier_count = np.sum(inliers) if inliers is not None else len(src_points)
                self.logger.debug(f"RANSAC: {inlier_count}/{len(src_points)} inliers")

            else:
                # Use exact solution with 3 points
                matrix = cv2.getAffineTransform(src_points, dst_points)
                inliers = np.ones((len(src_points), 1), dtype=np.uint8)

            return {
                'success': True,
                'matrix': matrix.tolist(),
                'src_points': src_points.tolist(),
                'dst_points': dst_points.tolist(),
                'correspondences': correspondence_info,
                'inlier_count': int(np.sum(inliers)) if inliers is not None else len(src_points),
                'total_correspondences': len(src_points)
            }

        except Exception as e:
            return {'success': False, 'error': f'Transformation calculation failed: {str(e)}'}

    def _find_feature_correspondences(self, ref_triplet: List, query_triplet: List) -> List[Tuple]:
        """
        Find individual feature correspondences between two triangle triplets.

        Uses spatial proximity and feature similarity to establish correspondences.

        Args:
            ref_triplet: List of reference HybridFeature objects
            query_triplet: List of query HybridFeature objects

        Returns:
            List of (ref_feature, query_feature, confidence) tuples
        """
        correspondences = []

        # Calculate all pairwise distances and similarities
        for ref_feat in ref_triplet:
            best_match = None
            best_confidence = 0.0

            for query_feat in query_triplet:
                # Skip if already used in a correspondence
                if any(qf == query_feat for _, qf, _ in correspondences):
                    continue

                # Feature type compatibility
                type_match = 1.0 if ref_feat.feature_type == query_feat.feature_type else 0.5

                # Confidence similarity
                conf_similarity = 1.0 - abs(ref_feat.confidence - query_feat.confidence)

                # Combined confidence score (prioritize type match and confidence)
                # Note: spatial distance is expected to be large due to transformation
                confidence = 0.6 * type_match + 0.4 * conf_similarity

                if confidence > best_confidence:
                    best_confidence = confidence
                    best_match = query_feat

            # Accept correspondence if confidence is reasonable
            if best_match is not None and best_confidence > 0.3:
                correspondences.append((ref_feat, best_match, best_confidence))

        # Sort by confidence (best first)
        correspondences.sort(key=lambda x: x[2], reverse=True)

        return correspondences

    def _log_status(self, message: str):
        """Log status message with timestamp."""
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        formatted_message = f"[{timestamp}] {message}"

        if self.status_callback:
            self.status_callback(formatted_message)

        if self.debug:
            self.logger.info(message)

    def _save_debug_image(self, image: np.ndarray, features: List[HybridFeature],
                         suffix: str):
        """Save debug image with feature annotations."""
        if not self.debug_screenshots_folder:
            return

        try:
            # Create annotated image
            annotated = image.copy()

            # Draw features
            for feature in features:
                x, y = int(feature.pixel_x), int(feature.pixel_y)

                if feature.feature_type == "flake":
                    # Draw flakes as green circles
                    cv2.circle(annotated, (x, y), 5, (0, 255, 0), 2)
                    cv2.putText(annotated, f"F:{feature.id}", (x+10, y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
                else:
                    # Draw edge keypoints as blue circles
                    cv2.circle(annotated, (x, y), 3, (255, 0, 0), 2)
                    cv2.putText(annotated, f"E:{feature.id}", (x+10, y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)

            # Save image
            timestamp = int(time.time())
            filename = f"hybrid_corner_{suffix}_{timestamp}.png"
            filepath = os.path.join(self.debug_screenshots_folder, filename)
            cv2.imwrite(filepath, annotated)

            self._log_status(f"Debug image saved: {filename}")

        except Exception as e:
            self.logger.error(f"Failed to save debug image: {str(e)}")

    def _save_match_visualization(self, matches: List[Dict], suffix: str):
        """Save visualization of feature matches."""
        if not self.debug_screenshots_folder:
            return

        try:
            # Create a simple text file with match information
            timestamp = int(time.time())
            filename = f"hybrid_matches_{suffix}_{timestamp}.txt"
            filepath = os.path.join(self.debug_screenshots_folder, filename)

            with open(filepath, 'w') as f:
                f.write(f"Hybrid Corner Feature Matches - {datetime.now()}\n")
                f.write("=" * 50 + "\n\n")

                for i, match in enumerate(matches):
                    f.write(f"Match {i+1}:\n")
                    f.write(f"  Quality: {match['match_quality']:.3f}\n")
                    f.write(f"  Hash Key: {match['hash_key']}\n")

                    # Query features
                    f.write("  Query Features:\n")
                    for j, feature in enumerate(match['query_triplet']):
                        f.write(f"    {j+1}. {feature.feature_type} {feature.id} at ({feature.x_um:.2f}, {feature.y_um:.2f})\n")

                    # Reference features
                    f.write("  Reference Features:\n")
                    for j, feature in enumerate(match['reference_triplet']['features']):
                        f.write(f"    {j+1}. {feature.feature_type} {feature.id} at ({feature.x_um:.2f}, {feature.y_um:.2f})\n")

                    f.write("\n")

            self._log_status(f"Match visualization saved: {filename}")

        except Exception as e:
            self.logger.error(f"Failed to save match visualization: {str(e)}")

    def _serialize_hash_table(self, hash_table: Dict[str, List[Dict]]) -> Dict[str, List[Dict]]:
        """
        Serialize hash table by converting HybridFeature objects to dictionaries.

        Args:
            hash_table: Hash table with HybridFeature objects

        Returns:
            Hash table with serialized feature dictionaries
        """
        serialized_table = {}

        for hash_key, triplet_list in hash_table.items():
            serialized_triplets = []
            for triplet_data in triplet_list:
                # Convert HybridFeature objects to dictionaries
                serialized_features = []
                for feature in triplet_data['features']:
                    if isinstance(feature, HybridFeature):
                        serialized_features.append(asdict(feature))
                    else:
                        serialized_features.append(feature)  # Already a dictionary

                # Create serialized triplet data
                serialized_triplet = triplet_data.copy()
                serialized_triplet['features'] = serialized_features
                serialized_triplets.append(serialized_triplet)

            serialized_table[hash_key] = serialized_triplets

        return serialized_table

    def _parse_hybrid_feature_string(self, feature_str: str) -> HybridFeature:
        """
        Parse a string representation of a HybridFeature back to a HybridFeature object.

        Args:
            feature_str: String representation of HybridFeature (e.g., from repr())

        Returns:
            HybridFeature object
        """
        try:
            # Extract the parameters from the string representation
            # Format: HybridFeature(id='...', feature_type='...', x_um=..., ...)

            # Remove the class name and parentheses
            if feature_str.startswith('HybridFeature(') and feature_str.endswith(')'):
                params_str = feature_str[14:-1]  # Remove 'HybridFeature(' and ')'
            else:
                raise ValueError(f"Invalid HybridFeature string format: {feature_str[:100]}...")

            # Parse the parameters using a more robust approach
            params = {}

            # Split by comma, but be careful with nested structures
            param_parts = []
            current_part = ""
            paren_depth = 0
            bracket_depth = 0
            brace_depth = 0
            in_quotes = False
            quote_char = None

            for char in params_str:
                if char in ['"', "'"] and not in_quotes:
                    in_quotes = True
                    quote_char = char
                elif char == quote_char and in_quotes:
                    in_quotes = False
                    quote_char = None
                elif not in_quotes:
                    if char == '(':
                        paren_depth += 1
                    elif char == ')':
                        paren_depth -= 1
                    elif char == '[':
                        bracket_depth += 1
                    elif char == ']':
                        bracket_depth -= 1
                    elif char == '{':
                        brace_depth += 1
                    elif char == '}':
                        brace_depth -= 1
                    elif char == ',' and paren_depth == 0 and bracket_depth == 0 and brace_depth == 0:
                        param_parts.append(current_part.strip())
                        current_part = ""
                        continue

                current_part += char

            if current_part.strip():
                param_parts.append(current_part.strip())

            # Parse each parameter
            for part in param_parts:
                if '=' in part:
                    # Find the first '=' that's not inside quotes or nested structures
                    eq_pos = -1
                    in_quotes = False
                    quote_char = None
                    depth = 0

                    for i, char in enumerate(part):
                        if char in ['"', "'"] and not in_quotes:
                            in_quotes = True
                            quote_char = char
                        elif char == quote_char and in_quotes:
                            in_quotes = False
                            quote_char = None
                        elif not in_quotes:
                            if char in '([{':
                                depth += 1
                            elif char in ')]}':
                                depth -= 1
                            elif char == '=' and depth == 0:
                                eq_pos = i
                                break

                    if eq_pos > 0:
                        key = part[:eq_pos].strip()
                        value = part[eq_pos+1:].strip()

                        # Parse the value based on its type
                        if value == 'None':
                            params[key] = None
                        elif value.startswith("'") and value.endswith("'"):
                            params[key] = value[1:-1]  # Remove quotes
                        elif value.startswith('"') and value.endswith('"'):
                            params[key] = value[1:-1]  # Remove quotes
                        elif value.startswith('{') and value.endswith('}'):
                            # Dictionary - use eval carefully
                            params[key] = eval(value)
                        elif value.startswith('[') and value.endswith(']'):
                            # List - use eval carefully
                            params[key] = eval(value)
                        elif value.replace('.', '').replace('-', '').isdigit():
                            # Numeric value
                            if '.' in value:
                                params[key] = float(value)
                            else:
                                params[key] = int(value)
                        else:
                            # Try to evaluate as-is
                            try:
                                params[key] = eval(value)
                            except:
                                params[key] = value  # Keep as string if eval fails

            # Create HybridFeature object
            return HybridFeature(**params)

        except Exception as e:
            self._log_status(f"Error parsing HybridFeature string: {str(e)}")
            self._log_status(f"String was: {feature_str[:200]}...")
            raise RuntimeError(f"Failed to parse HybridFeature string: {str(e)}")


# Utility functions for reference file management

def save_hybrid_reference(reference_data: Dict, filepath: str) -> bool:
    """
    Save hybrid corner reference data to file.

    Args:
        reference_data: Reference data dictionary
        filepath: Path to save the reference file

    Returns:
        True if successful, False otherwise
    """
    try:
        logger = logging.getLogger(__name__)
        save_start_time = time.time()

        # Calculate data size for monitoring
        hash_table = reference_data.get('hash_table', {})
        total_triplets = sum(len(bucket) for bucket in hash_table.values())
        total_buckets = len(hash_table)

        logger.info(f"Saving hybrid reference with {total_buckets} hash buckets and {total_triplets} triplets")

        # Ensure directory exists (only if filepath has a directory component)
        dir_path = os.path.dirname(filepath)
        if dir_path:
            os.makedirs(dir_path, exist_ok=True)

        # Save with compact JSON format to reduce file size
        logger.info("Writing reference data to file...")
        json_start_time = time.time()

        with open(filepath, 'w') as f:
            # Use compact JSON format (no indentation) to reduce file size
            json.dump(reference_data, f, separators=(',', ':'), default=str)

        json_duration = time.time() - json_start_time
        save_duration = time.time() - save_start_time

        # Get file size for monitoring
        file_size = os.path.getsize(filepath)
        file_size_mb = file_size / (1024 * 1024)

        logger.info(f"Reference file saved successfully:")
        logger.info(f"  - Total save time: {save_duration:.2f} seconds")
        logger.info(f"  - JSON write time: {json_duration:.2f} seconds")
        logger.info(f"  - File size: {file_size_mb:.2f} MB")
        logger.info(f"  - Triplets per MB: {total_triplets / max(file_size_mb, 0.001):.0f}")

        return True

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to save hybrid reference: {str(e)}")
        logger.error(f"Exception details: {type(e).__name__}: {str(e)}")
        return False


def load_hybrid_reference(filepath: str) -> Optional[Dict]:
    """
    Load hybrid corner reference data from file.

    Args:
        filepath: Path to the reference file

    Returns:
        Reference data dictionary or None if failed
    """
    try:
        with open(filepath, 'r') as f:
            reference_data = json.load(f)

        # Validate format
        if reference_data.get('format_version') != '3.0':
            logging.getLogger(__name__).warning(
                f"Reference file format version mismatch: {reference_data.get('format_version')} != 3.0"
            )

        if reference_data.get('alignment_method') != 'hybrid_corner':
            logging.getLogger(__name__).warning(
                f"Alignment method mismatch: {reference_data.get('alignment_method')} != hybrid_corner"
            )

        return reference_data

    except Exception as e:
        logging.getLogger(__name__).error(f"Failed to load hybrid reference: {str(e)}")
        return None


def apply_transformation_to_coordinates(coordinates: List[Tuple[float, float]],
                                       transformation_matrix: np.ndarray) -> List[Tuple[float, float]]:
    """
    Apply affine transformation to a list of coordinates.

    Args:
        coordinates: List of (x, y) coordinate tuples
        transformation_matrix: 2x3 affine transformation matrix

    Returns:
        List of transformed (x, y) coordinate tuples
    """
    if not coordinates:
        return []

    # Convert to numpy array
    points = np.array(coordinates, dtype=np.float32)

    # Add homogeneous coordinate
    ones = np.ones((points.shape[0], 1), dtype=np.float32)
    homogeneous_points = np.hstack([points, ones])

    # Apply transformation
    transformed_points = homogeneous_points @ transformation_matrix.T

    # Convert back to list of tuples
    return [(float(p[0]), float(p[1])) for p in transformed_points]


def create_hybrid_reference_filename(scan_folder: str) -> str:
    """
    Create a standardized filename for hybrid reference files using folder name prefix.

    Args:
        scan_folder: Base folder for the scan

    Returns:
        Full path to the reference file with format: {folder_name}_hybrid_reference.json
    """
    # Extract folder name from scan_folder path
    folder_name = os.path.basename(scan_folder.rstrip(os.sep))

    # Create filename with folder name prefix
    filename = f"{folder_name}_hybrid_reference.json"
    return os.path.join(scan_folder, filename)
