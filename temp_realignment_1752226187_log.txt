================================================================================
SCANNING OPERATION LOG - 2025-07-11 17:29:47
================================================================================
Mode: adaptive
Output CSV: temp_realignment_1752226187.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:/A.Members/张恩浩/python/transfer/20250711
Debug Screenshots: Z:/A.Members/张恩浩/python/transfer/20250711\debug_screenshots
================================================================================

[2025-07-11 17:29:47.080] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-11 17:29:47.090] [INFO] [SYSTEM] Using custom scan folder: 20250711
[2025-07-11 17:29:47.101] [INFO] [SUCCESS] ✓ Camera streaming already active
[2025-07-11 17:29:47.135] [INFO] [STATUS] === HYBRID CORNER RE-ALIGNMENT MODE ===
[2025-07-11 17:29:47.144] [INFO] [STATUS] === STARTING COMPLETE QUICK RE-ALIGNMENT WORKFLOW ===
[2025-07-11 17:29:47.154] [INFO] [STATUS] Using scan folder: Z:/A.Members/张恩浩/python/transfer/20250711
[2025-07-11 17:29:47.163] [INFO] [STATUS] Loading original scan data...
[2025-07-11 17:29:47.177] [INFO] [STATUS] Found original CSV: Z:/A.Members/张恩浩/python/transfer/20250711\20250711_flake_data.csv
[2025-07-11 17:29:47.197] [INFO] [STATUS] Found reference JSON: Z:/A.Members/张恩浩/python/transfer/20250711\20250711_hybrid_reference.json
[2025-07-11 17:29:47.206] [INFO] [STATUS] Loading original flake database...
[2025-07-11 17:29:47.247] [INFO] [STATUS] Loaded 108 original flakes
[2025-07-11 17:29:47.400] [INFO] [STATUS] Loaded reference with 154 features
[2025-07-11 17:29:47.434] [INFO] [WORKFLOW] Finding current upper-left corner position...
[2025-07-11 17:29:47.444] [INFO] [CALIBRATION] Setting zero reference point for re-alignment...
[2025-07-11 17:29:47.574] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-11 17:29:47.589] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-11 17:29:48.423] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-07-11 17:29:50.238] [INFO] [POSITION] Position feedback: (9.73, 0.00) μm
[2025-07-11 17:29:52.574] [INFO] [POSITION] Position feedback: (176.92, 0.00) μm
[2025-07-11 17:29:54.886] [INFO] [POSITION] Position feedback: (349.82, 0.00) μm
[2025-07-11 17:29:57.316] [INFO] [POSITION] Position feedback: (528.01, 0.00) μm
[2025-07-11 17:29:59.651] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-07-11 17:29:59.797] [INFO] [POSITION] Position feedback: (691.17, 0.00) μm
[2025-07-11 17:30:00.023] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-07-11 17:30:02.105] [INFO] [POSITION] Position feedback: (691.17, 25.61) μm
[2025-07-11 17:30:04.464] [INFO] [POSITION] Position feedback: (691.17, 139.89) μm
[2025-07-11 17:30:06.944] [INFO] [POSITION] Position feedback: (691.17, 270.67) μm
[2025-07-11 17:30:09.403] [INFO] [POSITION] Position feedback: (691.17, 371.83) μm
[2025-07-11 17:30:11.834] [INFO] [POSITION] Position feedback: (691.17, 500.07) μm
[2025-07-11 17:30:14.023] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-07-11 17:30:14.175] [INFO] [POSITION] Position feedback: (691.17, 612.87) μm
[2025-07-11 17:30:14.687] [INFO] [WORKFLOW] Starting position (rotation-robust): (613.0, 691.2) μm
[2025-07-11 17:30:14.697] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-07-11 17:30:14.794] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-11 17:30:14.804] [INFO] [STATUS] Skipping reference creation (re-alignment mode)
[2025-07-11 17:30:14.815] [INFO] [SUCCESS] ✓ Corner found for re-alignment - preserving original reference
[2025-07-11 17:30:14.829] [INFO] [STATUS] Current upper-left corner found at: (0.00, 0.00) μm
[2025-07-11 17:30:14.839] [INFO] [STATUS] Capturing current corner screenshot...
[2025-07-11 17:30:15.328] [INFO] [STATUS] Current corner image saved: Z:/A.Members/张恩浩/python/transfer/20250711\20250711_realigned_1752226215.png
[2025-07-11 17:30:15.339] [INFO] [STATUS] Performing feature detection and matching...
[2025-07-11 17:30:15.349] [INFO] [WORKFLOW] [17:30:15.349] Starting hybrid corner re-alignment...
[2025-07-11 17:30:15.359] [INFO] [STATUS] [17:30:15.359] Moving to reference corner: (0.00, 0.00) μm
[2025-07-11 17:30:15.504] [INFO] [POSITION] Position feedback: (0.00, 0.00) μm
[2025-07-11 17:30:16.566] [INFO] [STATUS] [17:30:16.566] Found current corner: (0.00, 0.00) μm
[2025-07-11 17:30:16.998] [INFO] [STATUS] [17:30:16.998] Detecting current corner features...
[2025-07-11 17:30:17.172] [INFO] [STATUS] [17:30:17.172] Loading and deserializing reference hash table...
[2025-07-11 17:30:20.284] [INFO] [SUCCESS] [17:30:20.284] ✓ Deserialized 1774 hash buckets with 5000 triplets
[2025-07-11 17:30:20.304] [INFO] [WORKFLOW] [17:30:20.304] Finding feature matches using geometric hashing...
[2025-07-11 17:30:32.384] [INFO] [STATUS] [17:30:32.384] Found 9 feature matches
[2025-07-11 17:30:32.396] [INFO] [STATUS] [17:30:32.396] Transformation matrix calculated successfully
[2025-07-11 17:30:32.418] [INFO] [SUCCESS] ✓ Re-alignment successful!
[2025-07-11 17:30:32.428] [INFO] [STATUS] Applying transformation to original flake coordinates...
[2025-07-11 17:30:32.441] [INFO] [SUCCESS] ✓ Transformed 108 flake coordinates
[2025-07-11 17:30:32.451] [INFO] [STATUS] === QUICK RE-ALIGNMENT COMPLETED SUCCESSFULLY ===
[2025-07-11 17:30:32.461] [INFO] [SUCCESS] ✓ 108 flakes ready for navigation
[2025-07-11 17:30:32.471] [INFO] [SUCCESS] ✓ Ready for flake selector integration
[2025-07-11 17:30:32.502] [INFO] [SUCCESS] ✓ Quick hybrid corner re-alignment completed successfully!
[2025-07-11 17:30:32.513] [INFO] [WORKFLOW] HYBRID-REALIGNMENT workflow completed - SUCCESS - Transformation matrix calculated
[2025-07-11 17:30:32.523] [INFO] [STATUS] Loading CSV file: Z:/A.Members/张恩浩/python/transfer/20250711\20250711_flake_data.csv
[2025-07-11 17:30:32.554] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.573] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.582] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.592] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.603] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.612] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.621] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.631] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.641] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.651] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.660] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.672] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.682] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.692] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.701] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.711] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.721] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.733] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.743] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.754] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.765] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.780] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.791] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.799] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.809] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.819] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.829] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.839] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.848] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.858] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.867] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.876] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.886] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.895] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.906] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.916] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.926] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.939] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.950] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.960] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.971] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.981] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:32.993] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.004] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.014] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.024] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.034] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.044] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.054] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.068] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.081] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.092] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.102] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.113] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.123] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.133] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.143] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.154] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.164] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.174] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.183] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.194] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.204] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.213] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.223] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.233] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.243] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.253] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.264] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.276] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.285] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.295] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.305] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.315] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.325] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.335] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.345] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.354] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.363] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.373] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.383] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.393] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.405] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.419] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.429] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.438] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.449] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.461] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.470] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.489] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.501] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.513] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.524] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.540] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.559] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.569] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.578] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.589] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.598] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.611] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.622] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.632] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.642] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.652] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.661] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.673] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.685] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.695] [ERROR] [ERROR] Error processing row: 'rotation_radians'
[2025-07-11 17:30:33.705] [INFO] [SUCCESS] Successfully transformed 0 flakes
