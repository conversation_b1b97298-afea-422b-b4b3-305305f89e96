================================================================================
SCANNING OPERATION LOG - 2025-07-10 18:28:55
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752143331\scan_1752143331_flake_data.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752143331
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752143331\debug_screenshots
================================================================================

[2025-07-10 18:28:55.326] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-10 18:28:55.343] [INFO] [SYSTEM] Using custom scan folder: scan_1752143331
[2025-07-10 18:28:55.359] [INFO] [SUCCESS] ✓ Camera streaming already active
[2025-07-10 18:28:55.372] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-10 18:28:55.474] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-10 18:28:55.500] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-10 18:28:56.277] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-07-10 18:28:58.153] [INFO] [POSITION] Position feedback: (4.02, 0.00) μm
[2025-07-10 18:29:00.533] [INFO] [POSITION] Position feedback: (176.71, 0.00) μm
[2025-07-10 18:29:02.883] [INFO] [POSITION] Position feedback: (362.94, 0.00) μm
[2025-07-10 18:29:05.203] [INFO] [POSITION] Position feedback: (522.51, 0.00) μm
[2025-07-10 18:29:07.623] [INFO] [POSITION] Position feedback: (700.91, 0.00) μm
[2025-07-10 18:29:10.003] [INFO] [POSITION] Position feedback: (873.38, 0.00) μm
[2025-07-10 18:29:12.223] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-07-10 18:29:12.422] [INFO] [POSITION] Position feedback: (1036.76, 0.00) μm
[2025-07-10 18:29:12.646] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-07-10 18:29:14.373] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-07-10 18:29:14.543] [INFO] [POSITION] Position feedback: (1036.76, 0.00) μm
[2025-07-10 18:29:15.054] [INFO] [WORKFLOW] Starting position (rotation-robust): (0.0, 1036.8) μm
[2025-07-10 18:29:15.065] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-07-10 18:29:15.182] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-10 18:29:15.192] [INFO] [STATUS] Creating automatic hybrid corner reference...
[2025-07-10 18:29:15.202] [INFO] [STATUS] Creating hybrid corner reference from current position...
[2025-07-10 18:29:15.651] [INFO] [SUCCESS] ✓ Corner reference image saved: Z:\A.Members\张恩浩\python\transfer\scan_1752143331\scan_1752143331_corner_image.png
[2025-07-10 18:29:15.662] [INFO] [STATUS] Detecting flakes in corner region...
[2025-07-10 18:29:19.808] [INFO] [STATUS] Found 0 flakes in corner region
[2025-07-10 18:29:19.819] [INFO] [STATUS] [18:29:19.819] Creating hybrid corner reference...
[2025-07-10 18:29:19.893] [INFO] [STATUS] [18:29:19.893] Current stage position: (-256.49, 0.00) μm
[2025-07-10 18:29:19.903] [INFO] [STATUS] [18:29:19.903] Detecting hybrid features (flakes + edge keypoints)...
[2025-07-10 18:29:20.100] [INFO] [STATUS] [18:29:20.100] Detected 151 total features:
[2025-07-10 18:29:20.111] [INFO] [STATUS] [18:29:20.111]   - 0 flakes
[2025-07-10 18:29:20.121] [INFO] [STATUS] [18:29:20.121]   - 151 edge keypoints
[2025-07-10 18:29:20.132] [INFO] [STATUS] [18:29:20.132] Generating geometric hash table...
[2025-07-10 18:29:20.292] [INFO] [STATUS] [18:29:20.292] Hash table generation completed in 0.14 seconds
[2025-07-10 18:29:20.302] [INFO] [STATUS] [18:29:20.302] Generated 2628 hash buckets
[2025-07-10 18:29:20.316] [INFO] [STATUS] [18:29:20.316] Total triplets stored: 5000
[2025-07-10 18:29:20.341] [INFO] [STATUS] [18:29:20.341] Hybrid corner reference created successfully with 151 features
[2025-07-10 18:29:20.350] [INFO] [SUCCESS] ✓ Automatic hybrid corner reference created successfully
[2025-07-10 18:29:20.363] [INFO] [STATUS] DEBUG: About to save reference to: Z:\A.Members\张恩浩\python\transfer\scan_1752143331\scan_1752143331_hybrid_reference.json
[2025-07-10 18:29:20.372] [INFO] [STATUS] DEBUG: Reference data keys: ['format_version', 'alignment_method', 'creation_timestamp', 'corner_origin_abs', 'features', 'hash_table', 'feature_counts', 'detection_methods', 'metadata', 'edge_detection_method', 'creation_mode', 'scan_mode', 'corner_image_file']
[2025-07-10 18:29:21.779] [INFO] [STATUS] DEBUG: save_hybrid_reference returned: True
[2025-07-10 18:29:21.806] [INFO] [SUCCESS] ✓ Reference saved to: Z:\A.Members\张恩浩\python\transfer\scan_1752143331\scan_1752143331_hybrid_reference.json
[2025-07-10 18:29:21.816] [INFO] [SUCCESS] ✓ MANDATORY hybrid corner reference creation completed successfully
[2025-07-10 18:29:21.825] [INFO] [STATUS] DEBUG: About to return True from _create_automatic_hybrid_corner_reference
[2025-07-10 18:29:21.843] [INFO] [WORKFLOW] Starting adaptive scan...
[2025-07-10 18:29:21.877] [INFO] [WORKFLOW] 
=== Starting Row 0 ===
[2025-07-10 18:29:21.886] [INFO] [STATUS] Scanning row 0 rightward...
[2025-07-10 18:29:27.509] [INFO] [STATUS] Position 0-0 performance:
[2025-07-10 18:29:27.527] [INFO] [STATUS]   - Detection: 3.171s
[2025-07-10 18:29:27.537] [INFO] [STATUS]   - Processing: 0.000s
[2025-07-10 18:29:27.547] [INFO] [STATUS]   - Data writing: 0.000s
[2025-07-10 18:29:27.556] [INFO] [STATUS]   - Total: 3.171s
[2025-07-10 18:29:27.566] [INFO] [STATUS]   - Flakes found: 0
[2025-07-10 18:29:27.577] [INFO] [PROGRESS] Progress: 1/11 (9.1%)
[2025-07-10 18:29:27.587] [INFO] [STATUS] Position (0,0): Found 0 flakes
[2025-07-10 18:29:27.752] [INFO] [POSITION] Position feedback: (-444.20, 0.00) μm
