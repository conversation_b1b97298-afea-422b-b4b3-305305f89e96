================================================================================
SCANNING OPERATION LOG - 2025-07-11 17:20:28
================================================================================
Mode: adaptive
Output CSV: temp_realignment_1752225628.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:/A.Members/张恩浩/python/transfer/20250711
Debug Screenshots: Z:/A.Members/张恩浩/python/transfer/20250711\debug_screenshots
================================================================================

[2025-07-11 17:20:28.483] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-11 17:20:28.496] [INFO] [SYSTEM] Using custom scan folder: 20250711
[2025-07-11 17:20:28.506] [INFO] [SUCCESS] ✓ Camera streaming already active
[2025-07-11 17:20:28.542] [INFO] [STATUS] === HYBRID CORNER RE-ALIGNMENT MODE ===
[2025-07-11 17:20:28.552] [INFO] [STATUS] === STARTING COMPLETE QUICK RE-ALIGNMENT WORKFLOW ===
[2025-07-11 17:20:28.568] [INFO] [STATUS] Using scan folder: Z:/A.Members/张恩浩/python/transfer/20250711
[2025-07-11 17:20:28.596] [INFO] [STATUS] Loading original scan data...
[2025-07-11 17:20:28.629] [INFO] [STATUS] Found original CSV: Z:/A.Members/张恩浩/python/transfer/20250711\20250711_flake_data.csv
[2025-07-11 17:20:28.639] [INFO] [STATUS] Found reference JSON: Z:/A.Members/张恩浩/python/transfer/20250711\20250711_hybrid_reference.json
[2025-07-11 17:20:28.648] [INFO] [STATUS] Loading original flake database...
[2025-07-11 17:20:28.672] [INFO] [STATUS] Loaded 108 original flakes
[2025-07-11 17:20:28.854] [INFO] [STATUS] Loaded reference with 154 features
[2025-07-11 17:20:28.883] [INFO] [WORKFLOW] Finding current upper-left corner position...
[2025-07-11 17:20:28.895] [INFO] [CALIBRATION] Setting zero reference point for re-alignment...
[2025-07-11 17:20:29.016] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-11 17:20:29.029] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-11 17:20:29.913] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-07-11 17:20:31.625] [INFO] [POSITION] Position feedback: (4.02, 0.00) μm
[2025-07-11 17:20:33.836] [INFO] [POSITION] Position feedback: (176.92, 0.00) μm
[2025-07-11 17:20:36.126] [INFO] [POSITION] Position feedback: (372.89, 0.00) μm
[2025-07-11 17:20:38.386] [INFO] [POSITION] Position feedback: (528.01, 0.00) μm
[2025-07-11 17:20:40.698] [INFO] [POSITION] Position feedback: (708.31, 0.00) μm
[2025-07-11 17:20:43.135] [INFO] [POSITION] Position feedback: (868.09, 0.00) μm
[2025-07-11 17:20:45.515] [INFO] [POSITION] Position feedback: (1064.06, 0.00) μm
[2025-07-11 17:20:47.865] [INFO] [POSITION] Position feedback: (1227.01, 0.00) μm
[2025-07-11 17:20:50.175] [INFO] [POSITION] Position feedback: (1386.58, 0.00) μm
[2025-07-11 17:20:52.350] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-07-11 17:20:52.495] [INFO] [POSITION] Position feedback: (1555.03, 0.00) μm
[2025-07-11 17:20:52.707] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-07-11 17:20:54.575] [INFO] [POSITION] Position feedback: (1555.03, 9.52) μm
[2025-07-11 17:20:56.905] [INFO] [POSITION] Position feedback: (1555.03, 126.98) μm
[2025-07-11 17:20:59.245] [INFO] [POSITION] Position feedback: (1555.03, 262.42) μm
[2025-07-11 17:21:01.506] [INFO] [POSITION] Position feedback: (1555.03, 385.16) μm
[2025-07-11 17:21:03.894] [INFO] [POSITION] Position feedback: (1555.03, 507.90) μm
[2025-07-11 17:21:06.257] [INFO] [POSITION] Position feedback: (1555.03, 616.89) μm
[2025-07-11 17:21:08.554] [INFO] [POSITION] Position feedback: (1555.03, 745.14) μm
[2025-07-11 17:21:10.895] [INFO] [POSITION] Position feedback: (1555.03, 862.17) μm
[2025-07-11 17:21:13.274] [INFO] [POSITION] Position feedback: (1555.03, 984.70) μm
[2025-07-11 17:21:15.624] [INFO] [POSITION] Position feedback: (1555.03, 1107.23) μm
[2025-07-11 17:21:18.015] [INFO] [POSITION] Position feedback: (1555.03, 1235.05) μm
[2025-07-11 17:21:20.384] [INFO] [POSITION] Position feedback: (1555.03, 1352.72) μm
[2025-07-11 17:21:22.530] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-07-11 17:21:22.684] [INFO] [POSITION] Position feedback: (1555.03, 1471.01) μm
[2025-07-11 17:21:23.204] [INFO] [WORKFLOW] Starting position (rotation-robust): (1471.1, 1555.2) μm
[2025-07-11 17:21:23.215] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-07-11 17:21:23.364] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-11 17:21:23.373] [INFO] [STATUS] Skipping reference creation (re-alignment mode)
[2025-07-11 17:21:23.386] [INFO] [SUCCESS] ✓ Corner found for re-alignment - preserving original reference
[2025-07-11 17:21:23.399] [INFO] [STATUS] Current upper-left corner found at: (0.00, 0.00) μm
[2025-07-11 17:21:23.423] [INFO] [STATUS] Capturing current corner screenshot...
[2025-07-11 17:21:23.871] [INFO] [STATUS] Current corner image saved: Z:/A.Members/张恩浩/python/transfer/20250711\20250711_realigned_1752225683.png
[2025-07-11 17:21:23.882] [INFO] [STATUS] Performing feature detection and matching...
[2025-07-11 17:21:23.892] [INFO] [WORKFLOW] [17:21:23.892] Starting hybrid corner re-alignment...
[2025-07-11 17:21:23.902] [INFO] [STATUS] [17:21:23.902] Moving to reference corner: (0.00, 0.00) μm
[2025-07-11 17:21:24.054] [INFO] [POSITION] Position feedback: (0.00, 0.00) μm
[2025-07-11 17:21:25.107] [INFO] [STATUS] [17:21:25.107] Found current corner: (0.00, 0.00) μm
[2025-07-11 17:21:25.533] [INFO] [STATUS] [17:21:25.533] Detecting current corner features...
[2025-07-11 17:21:25.713] [INFO] [STATUS] [17:21:25.713] Loading and deserializing reference hash table...
[2025-07-11 17:21:28.695] [INFO] [SUCCESS] [17:21:28.695] ✓ Deserialized 1774 hash buckets with 5000 triplets
[2025-07-11 17:21:28.707] [INFO] [WORKFLOW] [17:21:28.707] Finding feature matches using geometric hashing...
[2025-07-11 17:21:44.400] [INFO] [STATUS] [17:21:44.400] Found 9 feature matches
[2025-07-11 17:21:44.423] [INFO] [STATUS] [17:21:44.423] Transformation matrix calculated successfully
[2025-07-11 17:21:44.433] [INFO] [STATUS] [17:21:44.433] Hybrid re-alignment failed: list indices must be integers or slices, not str
[2025-07-11 17:21:44.473] [INFO] [STATUS] ✗ Hybrid corner re-alignment failed: Re-alignment failed: Hybrid re-alignment failed: list indices must be integers or slices, not str
[2025-07-11 17:21:44.483] [ERROR] [WORKFLOW] HYBRID-REALIGNMENT workflow completed - FAILURE - Re-alignment failed: Hybrid re-alignment failed: list indices must be integers or slices, not str
