================================================================================
SCANNING OPERATION LOG - 2025-07-11 17:27:50
================================================================================
Mode: adaptive
Output CSV: temp_realignment_1752226070.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:/A.Members/张恩浩/python/transfer/20250711
Debug Screenshots: Z:/A.Members/张恩浩/python/transfer/20250711\debug_screenshots
================================================================================

[2025-07-11 17:27:50.583] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-11 17:27:50.594] [INFO] [SYSTEM] Using custom scan folder: 20250711
[2025-07-11 17:27:50.605] [INFO] [SUCCESS] ✓ Camera streaming already active
[2025-07-11 17:27:50.633] [INFO] [STATUS] === HYBRID CORNER RE-ALIGNMENT MODE ===
[2025-07-11 17:27:50.667] [INFO] [STATUS] === STARTING COMPLETE QUICK RE-ALIGNMENT WORKFLOW ===
[2025-07-11 17:27:50.678] [INFO] [STATUS] Using scan folder: Z:/A.Members/张恩浩/python/transfer/20250711
[2025-07-11 17:27:50.688] [INFO] [STATUS] Loading original scan data...
[2025-07-11 17:27:50.713] [INFO] [STATUS] Found original CSV: Z:/A.Members/张恩浩/python/transfer/20250711\20250711_flake_data.csv
[2025-07-11 17:27:50.724] [INFO] [STATUS] Found reference JSON: Z:/A.Members/张恩浩/python/transfer/20250711\20250711_hybrid_reference.json
[2025-07-11 17:27:50.740] [INFO] [STATUS] Loading original flake database...
[2025-07-11 17:27:50.774] [INFO] [STATUS] Loaded 108 original flakes
[2025-07-11 17:27:50.939] [INFO] [STATUS] Loaded reference with 154 features
[2025-07-11 17:27:50.960] [INFO] [WORKFLOW] Finding current upper-left corner position...
[2025-07-11 17:27:50.971] [INFO] [CALIBRATION] Setting zero reference point for re-alignment...
[2025-07-11 17:27:51.069] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-11 17:27:51.083] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-11 17:27:51.947] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-07-11 17:27:53.729] [INFO] [POSITION] Position feedback: (9.52, 0.00) μm
[2025-07-11 17:27:55.959] [INFO] [POSITION] Position feedback: (200.20, 0.00) μm
[2025-07-11 17:27:58.159] [INFO] [POSITION] Position feedback: (349.82, 0.00) μm
[2025-07-11 17:28:00.379] [INFO] [POSITION] Position feedback: (522.51, 0.00) μm
[2025-07-11 17:28:02.620] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-07-11 17:28:02.779] [INFO] [POSITION] Position feedback: (691.17, 0.00) μm
[2025-07-11 17:28:02.991] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-07-11 17:28:04.879] [INFO] [POSITION] Position feedback: (691.17, 9.73) μm
[2025-07-11 17:28:07.242] [INFO] [POSITION] Position feedback: (691.17, 132.27) μm
[2025-07-11 17:28:09.669] [INFO] [POSITION] Position feedback: (691.17, 249.08) μm
[2025-07-11 17:28:12.029] [INFO] [POSITION] Position feedback: (691.17, 384.74) μm
[2025-07-11 17:28:14.358] [INFO] [POSITION] Position feedback: (691.17, 507.69) μm
[2025-07-11 17:28:16.566] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-07-11 17:28:16.719] [INFO] [POSITION] Position feedback: (691.17, 612.87) μm
[2025-07-11 17:28:17.240] [INFO] [WORKFLOW] Starting position (rotation-robust): (613.0, 691.2) μm
[2025-07-11 17:28:17.250] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-07-11 17:28:17.370] [ERROR] [ERROR] ⚠ Warning: Failed to set zero reference: Set zero failed: Zero position verification failed: Y=0.000μm, X=612.870μm
[2025-07-11 17:28:17.393] [INFO] [STATUS] Current upper-left corner found at: (612.96, 691.18) μm
[2025-07-11 17:28:17.403] [INFO] [STATUS] Capturing current corner screenshot...
[2025-07-11 17:28:17.842] [INFO] [STATUS] Current corner image saved: Z:/A.Members/张恩浩/python/transfer/20250711\20250711_realigned_1752226097.png
[2025-07-11 17:28:17.852] [INFO] [STATUS] Performing feature detection and matching...
[2025-07-11 17:28:17.862] [INFO] [WORKFLOW] [17:28:17.862] Starting hybrid corner re-alignment...
[2025-07-11 17:28:17.873] [INFO] [STATUS] [17:28:17.873] Moving to reference corner: (0.00, 0.00) μm
[2025-07-11 17:28:18.019] [INFO] [POSITION] Position feedback: (0.00, 608.64) μm
[2025-07-11 17:28:19.290] [INFO] [STATUS] [17:28:19.290] Found current corner: (0.00, 0.00) μm
[2025-07-11 17:28:19.720] [INFO] [STATUS] [17:28:19.720] Detecting current corner features...
[2025-07-11 17:28:19.955] [INFO] [STATUS] [17:28:19.955] Loading and deserializing reference hash table...
[2025-07-11 17:28:22.995] [INFO] [SUCCESS] [17:28:22.995] ✓ Deserialized 1774 hash buckets with 5000 triplets
[2025-07-11 17:28:23.020] [INFO] [WORKFLOW] [17:28:23.019] Finding feature matches using geometric hashing...
[2025-07-11 17:28:31.801] [INFO] [STATUS] [17:28:31.801] Found 9 feature matches
[2025-07-11 17:28:31.830] [INFO] [STATUS] [17:28:31.830] Transformation matrix calculated successfully
[2025-07-11 17:28:31.842] [INFO] [SUCCESS] ✓ Re-alignment successful!
[2025-07-11 17:28:31.851] [INFO] [STATUS] Applying transformation to original flake coordinates...
[2025-07-11 17:28:31.861] [INFO] [SUCCESS] ✓ Transformed 108 flake coordinates
[2025-07-11 17:28:31.872] [INFO] [STATUS] === QUICK RE-ALIGNMENT COMPLETED SUCCESSFULLY ===
[2025-07-11 17:28:31.893] [INFO] [SUCCESS] ✓ 108 flakes ready for navigation
[2025-07-11 17:28:31.902] [INFO] [STATUS] ERROR: Exception in transformation application: unsupported format string passed to list.__format__
[2025-07-11 17:28:31.941] [ERROR] [ERROR] Traceback: Traceback (most recent call last):
  File "z:\A.Members\张恩浩\python\transfer\scanning.py", line 1785, in _apply_transformation_and_finalize
    self._emit_and_log_status(f"✓ Transformation confidence: {confidence:.3f}")
                                                             ^^^^^^^^^^^^^^^^
TypeError: unsupported format string passed to list.__format__

[2025-07-11 17:28:31.954] [INFO] [STATUS] ✗ Hybrid corner re-alignment failed: Exception in transformation application: unsupported format string passed to list.__format__
[2025-07-11 17:28:31.963] [ERROR] [WORKFLOW] HYBRID-REALIGNMENT workflow completed - FAILURE - Exception in transformation application: unsupported format string passed to list.__format__
