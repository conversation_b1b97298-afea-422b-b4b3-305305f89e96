================================================================================
SCANNING OPERATION LOG - 2025-07-11 11:56:37
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752206193\scan_1752206193_flake_data.csv
Grid Steps: 0 x 0
Edge Method: general
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752206193
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752206193\debug_screenshots
================================================================================

[2025-07-11 11:56:37.119] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-11 11:56:37.130] [INFO] [SYSTEM] Using custom scan folder: scan_1752206193
[2025-07-11 11:56:37.152] [INFO] [SUCCESS] ✓ Camera streaming already active
[2025-07-11 11:56:37.189] [INFO] [STATUS] === HYBRID CORNER RE-ALIGNMENT MODE ===
[2025-07-11 11:56:37.197] [INFO] [STATUS] === STARTING COMPLETE QUICK RE-ALIGNMENT WORKFLOW ===
[2025-07-11 11:56:37.209] [INFO] [STATUS] Using scan folder: Z:\A.Members\张恩浩\python\transfer\scan_1752206193
[2025-07-11 11:56:37.221] [INFO] [STATUS] Loading original scan data...
[2025-07-11 11:56:37.245] [INFO] [STATUS] ✗ Hybrid corner re-alignment failed: No flake data CSV file found in scan folder (expected: scan_1752206193_flake_data.csv)
[2025-07-11 11:56:37.259] [ERROR] [WORKFLOW] HYBRID-REALIGNMENT workflow completed - FAILURE - No flake data CSV file found in scan folder (expected: scan_1752206193_flake_data.csv)
